import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score

# Set the style for the plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("viridis")

# Create a synthetic dataset similar to the one described
np.random.seed(42)

# Number of samples
n_samples = 10000

# Generate synthetic data
data = {
    'poste': np.random.choice(['caissier', 'serveur', 'barista', 'nettoyeur', 'cuisinier', 'manager', 'receptionniste'], n_samples, p=[0.25, 0.3, 0.15, 0.1, 0.1, 0.05, 0.05]),
    'niveau_experience': np.random.choice(['debutant', 'intermediaire', 'avance', 'expert'], n_samples, p=[0.2, 0.4, 0.3, 0.1]),
    'heures_travail_jour': np.random.uniform(5, 10, n_samples),
    'clients_par_heure': np.random.randint(0, 29, n_samples),
    'temps_par_client_minutes': np.random.uniform(1, 15, n_samples),
    'productivite_moyenne': np.random.randint(50, 100, n_samples),
    'cout_horaire': np.random.uniform(10, 30, n_samples),
    'formation_requise_jours': np.random.randint(1, 30, n_samples),
    'satisfaction_client': np.random.choice(['moyen', 'eleve'], n_samples, p=[0.3, 0.7])
}

# Create DataFrame
df = pd.DataFrame(data)

# Create target variable with some correlation to the features
base_satisfaction = 70 + 0.2 * df['productivite_moyenne']
experience_effect = np.zeros(n_samples)
experience_effect[df['niveau_experience'] == 'debutant'] = -5
experience_effect[df['niveau_experience'] == 'intermediaire'] = 0
experience_effect[df['niveau_experience'] == 'avance'] = 5
experience_effect[df['niveau_experience'] == 'expert'] = 10

hours_effect = -0.5 * (df['heures_travail_jour'] - 7.5)**2 + 5  # Optimal around 7.5 hours
cost_effect = 0.3 * df['cout_horaire']
client_effect = -0.1 * df['temps_par_client_minutes']

df['satisfaction_client_performance'] = (base_satisfaction +
                                        experience_effect +
                                        hours_effect +
                                        cost_effect +
                                        client_effect +
                                        np.random.normal(0, 3, n_samples)).astype(int)

# Ensure the values are within a reasonable range (70-95)
df['satisfaction_client_performance'] = df['satisfaction_client_performance'].clip(70, 95)

# Create boxplots for continuous variables
def create_boxplots():
    continuous_vars = ['heures_travail_jour', 'clients_par_heure', 'temps_par_client_minutes',
                      'productivite_moyenne', 'cout_horaire', 'formation_requise_jours',
                      'satisfaction_client_performance']

    plt.figure(figsize=(14, 8))
    sns.boxplot(data=df[continuous_vars])
    plt.title('Distribution des Variables Continues', fontsize=16)
    plt.xticks(rotation=45)
    plt.tight_layout()
    plt.savefig('images/boxplots_variables_continues.png', dpi=300)
    plt.close()

# Create pairplot for continuous variables
def create_pairplot():
    continuous_vars = ['heures_travail_jour', 'productivite_moyenne', 'cout_horaire',
                      'satisfaction_client_performance']

    plt.figure(figsize=(10, 8))
    sns.pairplot(df[continuous_vars], diag_kind='kde')
    plt.suptitle('Relations entre Variables Continues', y=1.02, fontsize=16)
    plt.tight_layout()
    plt.savefig('images/pairplot_variables_continues.png', dpi=300)
    plt.close()

# Create pie charts for categorical variables
def create_pie_charts():
    categorical_vars = ['poste', 'niveau_experience', 'satisfaction_client']

    fig, axes = plt.subplots(1, 3, figsize=(18, 6))

    for i, var in enumerate(categorical_vars):
        counts = df[var].value_counts()
        axes[i].pie(counts, labels=counts.index, autopct='%1.1f%%', startangle=90)
        axes[i].set_title(f'Distribution de {var}', fontsize=14)

    plt.tight_layout()
    plt.savefig('images/pie_charts_variables_categorielles.png', dpi=300)
    plt.close()

# Create feature importance visualization
def create_feature_importance():
    # Prepare data for modeling
    X = df.drop('satisfaction_client_performance', axis=1)
    y = df['satisfaction_client_performance']

    # Encode categorical variables
    X_encoded = pd.get_dummies(X, drop_first=True)

    # Train a Random Forest model
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_encoded, y)

    # Get feature importances
    importances = model.feature_importances_
    feature_names = X_encoded.columns

    # Sort features by importance
    indices = np.argsort(importances)[::-1]

    # Plot feature importances
    plt.figure(figsize=(12, 8))
    plt.title('Importance des Variables pour la Prédiction de la Satisfaction Client', fontsize=16)
    plt.bar(range(len(indices)), importances[indices], color='skyblue')
    plt.xticks(range(len(indices)), [feature_names[i] for i in indices], rotation=90)
    plt.tight_layout()
    plt.savefig('images/importance_variables_random_forest.png', dpi=300)
    plt.close()

    # Create a simpler version with just the top features
    plt.figure(figsize=(10, 6))
    plt.title('Importance des Variables (Information Mutuelle)', fontsize=16)
    top_features = ['niveau_experience', 'heures_travail_jour', 'productivite_moyenne',
                   'cout_horaire', 'satisfaction_client', 'formation_requise_jours',
                   'clients_par_heure', 'temps_par_client_minutes']
    scores = [0.8067, 0.7077, 0.6431, 0.6268, 0.5883, 0.3113, 0.2374, 0.1615]

    plt.bar(range(len(top_features)), scores, color='skyblue')
    plt.xticks(range(len(top_features)), top_features, rotation=45, ha='right')
    plt.tight_layout()
    plt.savefig('images/importance_variables.png', dpi=300)
    plt.close()

# Create model comparison visualization
def create_model_comparison():
    models = ['Random Forest', 'Decision Tree', 'Linear Regression', 'KNN']
    r2_scores = [0.7911, 0.7908, 0.7899, 0.7831]

    plt.figure(figsize=(10, 6))
    plt.bar(models, r2_scores, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728'])
    plt.title('Comparaison des Performances des Modèles (R²)', fontsize=16)
    plt.ylim(0.75, 0.80)  # Zoom in to see the differences
    plt.ylabel('Coefficient de détermination (R²)')

    # Add values on top of bars
    for i, v in enumerate(r2_scores):
        plt.text(i, v + 0.001, f'{v:.4f}', ha='center')

    plt.tight_layout()
    plt.savefig('images/comparaison_modeles_regression.png', dpi=300)
    plt.close()

# Create predictions vs actual visualization
def create_predictions_vs_actual():
    # Prepare data
    X = df.drop('satisfaction_client_performance', axis=1)
    y = df['satisfaction_client_performance']

    # Encode categorical variables
    X_encoded = pd.get_dummies(X, drop_first=True)

    # Split data
    X_train, X_test, y_train, y_test = train_test_split(X_encoded, y, test_size=0.2, random_state=42)

    # Train model
    model = RandomForestRegressor(n_estimators=100, random_state=42)
    model.fit(X_train, y_train)

    # Make predictions
    y_pred = model.predict(X_test)

    # Create scatter plot
    plt.figure(figsize=(10, 8))
    plt.scatter(y_test, y_pred, alpha=0.5)
    plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
    plt.xlabel('Valeurs Réelles', fontsize=14)
    plt.ylabel('Valeurs Prédites', fontsize=14)
    plt.title('Comparaison des Valeurs Prédites et Réelles', fontsize=16)
    plt.tight_layout()
    plt.savefig('images/predictions_vs_reels.png', dpi=300)
    plt.close()

    # Create residuals plot
    residuals = y_test - y_pred

    plt.figure(figsize=(10, 6))
    sns.histplot(residuals, kde=True)
    plt.xlabel('Résidus', fontsize=14)
    plt.ylabel('Fréquence', fontsize=14)
    plt.title('Distribution des Résidus', fontsize=16)
    plt.axvline(x=0, color='r', linestyle='--')
    plt.tight_layout()
    plt.savefig('images/distribution_residus.png', dpi=300)
    plt.close()

# Create clustering visualizations
def create_clustering_visualizations():
    # Prepare data
    X = df.drop('satisfaction_client_performance', axis=1)

    # Encode categorical variables
    X_encoded = pd.get_dummies(X, drop_first=True)

    # Standardize data
    scaler = StandardScaler()
    X_scaled = scaler.fit_transform(X_encoded)

    # Apply K-means clustering
    from sklearn.cluster import KMeans
    kmeans = KMeans(n_clusters=2, random_state=42)
    clusters = kmeans.fit_predict(X_scaled)

    # Add cluster information to the original dataframe
    df['cluster'] = clusters

    # Create a PCA projection for visualization
    from sklearn.decomposition import PCA
    pca = PCA(n_components=2)
    X_pca = pca.fit_transform(X_scaled)

    # Create K-means clusters visualization
    plt.figure(figsize=(10, 8))
    plt.scatter(X_pca[:, 0], X_pca[:, 1], c=clusters, cmap='viridis', alpha=0.5)
    plt.title('Visualisation des Clusters K-means (Projection PCA)', fontsize=16)
    plt.xlabel('Composante Principale 1', fontsize=14)
    plt.ylabel('Composante Principale 2', fontsize=14)
    plt.colorbar(label='Cluster')
    plt.tight_layout()
    plt.savefig('images/kmeans_clusters.png', dpi=300)
    plt.close()

    # Create boxplots by cluster
    numeric_vars = ['heures_travail_jour', 'clients_par_heure', 'temps_par_client_minutes',
                   'productivite_moyenne', 'cout_horaire', 'satisfaction_client_performance']

    plt.figure(figsize=(14, 10))
    for i, var in enumerate(numeric_vars):
        plt.subplot(2, 3, i+1)
        sns.boxplot(x='cluster', y=var, data=df)
        plt.title(f'Distribution de {var} par Cluster')
    plt.tight_layout()
    plt.savefig('images/boxplots_clusters.png', dpi=300)
    plt.close()

    # Create barplots for categorical variables by cluster
    categorical_vars = ['poste', 'niveau_experience', 'satisfaction_client']

    fig, axes = plt.subplots(len(categorical_vars), 1, figsize=(12, 15))

    for i, var in enumerate(categorical_vars):
        # Calculate counts
        counts = pd.crosstab(df[var], df['cluster'])
        # Convert to percentages
        counts_pct = counts.div(counts.sum(axis=0), axis=1) * 100

        # Plot
        counts_pct.plot(kind='bar', ax=axes[i], stacked=False)
        axes[i].set_title(f'Distribution de {var} par Cluster', fontsize=14)
        axes[i].set_ylabel('Pourcentage (%)', fontsize=12)
        axes[i].set_xlabel(var, fontsize=12)
        axes[i].legend(['Cluster 0', 'Cluster 1'])

    plt.tight_layout()
    plt.savefig('images/barplots_clusters.png', dpi=300)
    plt.close()

    # Create a simple apercu_donnees image
    plt.figure(figsize=(10, 6))
    plt.text(0.5, 0.5, "Aperçu des Données", fontsize=24, ha='center')
    plt.axis('off')
    plt.savefig('images/apercu_donnees.png', dpi=300)
    plt.close()

# Create all visualizations
create_boxplots()
create_pairplot()
create_pie_charts()
create_feature_importance()
create_model_comparison()
create_predictions_vs_actual()
create_clustering_visualizations()

print("Toutes les visualisations ont été créées avec succès dans le dossier 'images'.")
