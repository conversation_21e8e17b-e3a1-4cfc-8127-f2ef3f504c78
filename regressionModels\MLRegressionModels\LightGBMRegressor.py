from lightgbm import LGBMRegressor
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import mean_squared_error, r2_score
from scipy.stats import randint, loguniform
import matplotlib.pyplot as plt
import numpy as np
from evaluationModels.evaluation_regressor import RegressionEvaluator

class Method_LightGBM_Regressor:
    def __init__(self):
        self.best_parameter = None

    def train_lightgbm(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'n_estimators': randint(100, 1000),
            'max_depth': randint(3, 20),
            'learning_rate': loguniform(0.01, 0.2),
            'num_leaves': randint(20, 150),
            'min_child_samples': randint(5, 30),
            'subsample': loguniform(0.5, 1.0),
            'colsample_bytree': loguniform(0.5, 1.0)
        }

        model = LGBMRegressor(random_state=random_state)

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring='neg_root_mean_squared_error',
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée (RMSE -): {random_search.best_score_:.4f}")

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_lightgbm_regressor(self, X_train, y_train, X_test, y_test):
        print("__________Entraînement du modèle LightGBM (Régression)__________")
        self.train_lightgbm(X_train, y_train)
        y_pred = self.predict(X_test)
        print("_________________Évaluation du modèle_________________")
        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()

    # def plot_feature_importance(self, feature_names=None, top_n=10):
    #     if self.best_parameter is None:
    #         raise ValueError("Le modèle n'a pas été entraîné.")
        
    #     importances = self.best_parameter.feature_importances_
    #     indices = np.argsort(importances)[::-1]

    #     if feature_names is None:
    #         feature_names = [f'Feature {i}' for i in range(len(importances))]

    #     top_indices = indices[:top_n]
    #     plt.figure(figsize=(10, 6))
    #     plt.title("Importance des variables")
    #     plt.bar(range(top_n), importances[top_indices], align="center")
    #     plt.xticks(range(top_n), [feature_names[i] for i in top_indices], rotation=45)
    #     plt.xlabel("Variables")
    #     plt.ylabel("Importance")
    #     plt.tight_layout()
    #     plt.show()
