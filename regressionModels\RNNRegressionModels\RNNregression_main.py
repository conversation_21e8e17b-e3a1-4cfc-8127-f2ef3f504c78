from regressionModels.RNNRegressionModels.GRURegressor import Method_GRU_Regressor
from regressionModels.RNNRegressionModels.LSTMRegressor import  Method_LSTM_Regressor


class RegressorRNNModels_main:
    def __init__(self):
        model_list = [
            ('LSTM', Method_LSTM_Regressor()),
            ('GRU', Method_GRU_Regressor())        ]
        self.models = {i + 1: model for i, model in enumerate(model_list)}

    def select_models(self):
        print("\n" + "="*80)
        print("Construction et évaluation des modèles de machine learning".center(80))
        print("="*80)
        print("Puisque vous avez un problème de régression")
        print("Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparés par des virgules (e.g., 1, 2):")

        while True:
            for idx, (name, _) in self.models.items():
                print(f"{idx}. {name}")

            selected_indices = input("Votre choix : ").split(',')
            selected_indices = [
                int(idx.strip()) for idx in selected_indices
                if idx.strip().isdigit() and int(idx.strip()) in self.models
            ]

            if selected_indices:
                return selected_indices
            else:
                print("Erreur : votre choix est invalide. Veuillez réessayer.")

    def models_selected_regressor(self, model_indices, X_train, y_train, X_test, y_test):
        for idx in model_indices:
            model_name, model = self.models[idx]
            method_name = f"run_{model_name.lower()}_regressor"

            if hasattr(model, method_name):
                print("\n" + "="*80)
                print(f"MÉTHODE : {model_name}".center(80))
                print("="*80)
                getattr(model, method_name)(X_train, y_train, X_test, y_test)
                print("\n" + "-"*80)
                print(f"Processus '{model_name}' terminé avec succès.".center(80))
                print("-"*80 + "\n")
                print("="*80 + "\n")
            else:
                print(f"Méthode '{method_name}' introuvable pour le modèle {model_name}")

    def run_models_selected_regressor(self, X_train, y_train, X_test, y_test):
        selected_indices = self.select_models()
        self.models_selected_regressor(selected_indices, X_train, y_train, X_test, y_test)
