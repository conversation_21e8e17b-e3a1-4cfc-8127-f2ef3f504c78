from sklearn.cluster import MiniBatchKMeans
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import euclidean_distances
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

class MiniBatchKMeansClustering:
    def __init__(self, data, max_clusters=10, batch_sizes=[16, 32, 64, 128, 256]):
        self.data = data
        self.max_clusters = max_clusters
        self.batch_sizes = batch_sizes
        self.best_k = None
        self.best_batch_size = None
        self.model = None
        self.labels = None
        self.centroids = None

    def _find_optimal_k_and_batch(self):
        best_score = -1
        results = []

        for k in range(2, self.max_clusters + 1):
            for batch_size in self.batch_sizes:
                model = MiniBatchKMeans(n_clusters=k, batch_size=batch_size, random_state=42, n_init=3)
                model.fit(self.data)
                labels = model.labels_
                score = silhouette_score(self.data, labels)
                results.append((k, batch_size, score))

                if score > best_score:
                    best_score = score
                    self.best_k = k
                    self.best_batch_size = batch_size

        self._plot_silhouette(results)
        print(f"✔️ Meilleure configuration : k = {self.best_k}, batch_size = {self.best_batch_size}, silhouette = {best_score:.4f}")

    def _fit_model(self):
        self.model = MiniBatchKMeans(n_clusters=self.best_k, batch_size=self.best_batch_size, random_state=42, n_init=3)
        self.model.fit(self.data)
        self.labels = self.model.labels_
        self.centroids = self.model.cluster_centers_

    def _describe_clusters(self):
        print("\nDescription des clusters :")
        for label in range(self.best_k):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        dims = self.data.shape[1]
        data = self.data.values if isinstance(self.data, pd.DataFrame) else self.data
        labels = self.labels
        centroids = self.centroids

        if dims == 2:
            X1, X2 = data[:, 0], data[:, 1]
            distances = euclidean_distances(data, centroids)
            radius = [np.max(distances[labels == i, i]) for i in range(len(centroids))]
            plt.scatter(X1, X2, c=labels, s=40)
            plt.scatter(centroids[:, 0], centroids[:, 1], c='red', marker='x', s=100, label='Centroides')
            for i, centroid in enumerate(centroids):
                circle = plt.Circle(centroid, radius[i], color='black', fill=False)
                plt.gca().add_patch(circle)
            plt.title("Clustering Mini-Batch K-Means (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.legend()
            plt.show()
        elif dims == 3:
            fig = plt.figure()
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(data[:, 0], data[:, 1], data[:, 2], c=labels, s=40)
            ax.scatter(centroids[:, 0], centroids[:, 1], centroids[:, 2], c='black', s=100, label='Centroides')
            ax.set_title("Clustering Mini-Batch K-Means (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.legend()
            plt.show()
        else:
            print("Visualisation disponible uniquement en 2D ou 3D.")

    def _plot_silhouette(self, results):
        import seaborn as sns
        df = pd.DataFrame(results, columns=["k", "batch_size", "silhouette"])
        pivot = df.pivot(index="batch_size", columns="k", values="silhouette")
        sns.heatmap(pivot, annot=True, fmt=".2f", cmap="viridis")
        plt.title("Score de silhouette pour chaque (k, batch_size)")
        plt.xlabel("Nombre de clusters (k)")
        plt.ylabel("Taille du batch")
        plt.show()

    def run_minibatch_kmeans_clustering(self):
        print("Recherche des meilleurs hyperparamètres (k, batch_size)...")
        self._find_optimal_k_and_batch()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
