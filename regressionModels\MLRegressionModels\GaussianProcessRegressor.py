from sklearn.gaussian_process import GaussianProcessRegressor
from sklearn.gaussian_process.kernels import <PERSON><PERSON>, Constant<PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>Kernel, DotProduct
from sklearn.model_selection import RandomizedSearchCV
from scipy.stats import uniform, randint as sp_randint
from sklearn.metrics import mean_squared_error, r2_score
from evaluationModels.evaluation_regressor import RegressionEvaluator

class Method_GaussianProcess_Regressor:
    def __init__(self):
        self.best_gpr = None

    def train_gpr(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("_________________Entraînement du modèle GPR pour la régression_________________")
        print("Veuillez patienter quelques instants...")

        # Définition des kernels
        kernels = [
            ConstantKernel() * RBF(),
            ConstantKernel() * RBF() + <PERSON><PERSON>ernel(),
            ConstantKernel() * Matern(nu=1.5),
            ConstantKernel() * <PERSON>ern(nu=2.5),
            ConstantKernel() * DotProduct() + WhiteKernel()
        ]

        # Paramètres à optimiser
        param_dist = {
            'kernel': kernels,
            'alpha': [1e-10, 1e-8, 1e-6, 1e-4],
            'normalize_y': [True, False],
            'n_restarts_optimizer': sp_randint(0, 11)
        }

        model = GaussianProcessRegressor(random_state=random_state)

        random_search = RandomizedSearchCV(
            model,
            param_distributions=param_dist,
            n_iter=n_iter,
            cv=cv,
            scoring='neg_mean_squared_error',
            random_state=random_state,
            n_jobs=-1,
            verbose=1
        )

        random_search.fit(X_train, y_train)
        self.best_gpr = random_search.best_estimator_

        print(f"Le modèle GPR a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")
        return self

    def predict(self, X_test):
        if self.best_gpr is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        else:
            print("La prédiction avec les données de test...")

        return self.best_gpr.predict(X_test)

    def run_gaussian_process_regression_regressor(self, X_train, y_train, X_test, y_test):
        # Entraînement du modèle
        self.train_gpr(X_train, y_train)

        # Prédiction sur les données de test
        y_pred = self.predict(X_test)

        # Évaluation du modèle
        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
