import numpy as np
import matplotlib.pyplot as plt
from scipy.spatial.distance import pdist, squareform
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import euclidean_distances
import seaborn as sns
import pandas as pd
from mpl_toolkits.mplot3d import Axes3D

class DensityPeaksClustering:
    def __init__(self, data, dc_range=[1.0, 2.0, 3.0, 4.0, 5.0], max_k_range=range(2, 11)):
        self.data = data.values if hasattr(data, 'values') else data
        self.dc_range = dc_range
        self.max_k_range = max_k_range
        self.best_dc = None
        self.best_k = None
        self.best_score = -1
        self.results = {}

    def _compute_distances(self):
        return squareform(pdist(self.data, 'euclidean'))

    def _compute_density(self, distances, dc):
        return np.sum(np.exp(-(distances / dc) ** 2), axis=1) - 1

    def _compute_delta(self, distances, density):
        n = len(density)
        delta = np.zeros(n)
        nearest_higher = np.zeros(n, dtype=int)
        sorted_indices = np.argsort(-density)
        delta[sorted_indices[0]] = np.max(distances[sorted_indices[0]])
        nearest_higher[sorted_indices[0]] = -1
        for i in range(1, n):
            current_idx = sorted_indices[i]
            higher_indices = sorted_indices[:i]
            dist_to_higher = distances[current_idx, higher_indices]
            min_dist = np.min(dist_to_higher)
            delta[current_idx] = min_dist
            nearest_higher[current_idx] = higher_indices[np.argmin(dist_to_higher)]
        return delta, nearest_higher

    def _assign_clusters(self, density, delta, nearest_higher, distances, k):
        gamma = density * delta
        centers = np.argsort(-gamma)[:k]
        labels = -np.ones(len(density), dtype=int)
        for cluster_id, center in enumerate(centers):
            labels[center] = cluster_id
        sorted_indices = np.argsort(-density)
        for idx in sorted_indices:
            if labels[idx] == -1:
                labels[idx] = labels[nearest_higher[idx]]
        return labels, centers

    def _plot_heatmap(self, scores):
        df = pd.DataFrame(scores, columns=["dc", "k", "score"])
        df["dc"] = df["dc"].round(2)
        pivot = df.pivot(index="dc", columns="k", values="score")
        plt.figure(figsize=(10, 6))
        sns.heatmap(pivot, annot=True, fmt=".3f", cmap="viridis")
        plt.title("Score silhouette selon dc et k")
        plt.xlabel("Nombre de clusters (k)")
        plt.ylabel("Cutoff distance (%)")
        plt.show()

    def _describe_clusters(self, labels):
        print("\n Description des clusters :")
        for label in np.unique(labels):
            count = np.sum(labels == label)
            percent = 100 * count / len(labels)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self, labels, centers):
        dims = self.data.shape[1]
        if dims == 2:
            plt.figure(figsize=(10, 6))
            plt.scatter(self.data[:, 0], self.data[:, 1], c=labels, cmap='viridis', s=50)
            plt.scatter(self.data[centers, 0], self.data[centers, 1], c='red', marker='x', s=200, label='Centres')
            plt.title('Clustering DPC (2D)')
            plt.legend()
            plt.show()
        elif dims == 3:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(self.data[:, 0], self.data[:, 1], self.data[:, 2], c=labels, cmap='viridis', s=50)
            ax.scatter(self.data[centers, 0], self.data[centers, 1], self.data[centers, 2], 
                       c='red', marker='x', s=200, label='Centres')
            ax.set_title('Clustering DPC (3D)')
            ax.legend()
            plt.show()
        else:
            print("Visualisation uniquement pour données 2D ou 3D")

    def run_density_peaks_clustering(self):
        distances = self._compute_distances()
        scores = []

        for dc in self.dc_range:
            dc_val = np.percentile(distances, dc)
            density = self._compute_density(distances, dc_val)
            delta, nearest_higher = self._compute_delta(distances, density)
            for k in self.max_k_range:
                labels, centers = self._assign_clusters(density, delta, nearest_higher, distances, k)
                try:
                    score = silhouette_score(self.data, labels)
                    scores.append((dc, k, score))
                    if score > self.best_score:
                        self.best_score = score
                        self.best_dc = dc
                        self.best_k = k
                        self.results = {
                            'labels': labels,
                            'centers': centers,
                            'density': density,
                            'delta': delta,
                            'dc': dc_val,
                            'gamma': density * delta,
                            'nearest_higher': nearest_higher
                        }
                except:
                    continue

        self._plot_heatmap(scores)
        print(f"\n✔ Meilleur couple (dc={self.best_dc}%, k={self.best_k}) | Score silhouette : {self.best_score:.4f}")
        self._describe_clusters(self.results['labels'])
        self._visualize(self.results['labels'], self.results['centers'])

        return self.results
