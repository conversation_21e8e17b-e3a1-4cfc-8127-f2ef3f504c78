import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.decomposition import PCA
from sklearn.cluster import KMeans

# Set the style for the plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("viridis")

# Create a synthetic dataset similar to the one described
np.random.seed(42)

# Number of samples
n_samples = 10000

# Generate synthetic data
data = {
    'poste': np.random.choice(['caissier', 'serveur', 'barista', 'nettoyeur', 'cuisinier', 'manager', 'receptionniste'], n_samples, p=[0.25, 0.3, 0.15, 0.1, 0.1, 0.05, 0.05]),
    'niveau_experience': np.random.choice(['debutant', 'intermediaire', 'avance', 'expert'], n_samples, p=[0.2, 0.4, 0.3, 0.1]),
    'heures_travail_jour': np.random.uniform(5, 10, n_samples),
    'clients_par_heure': np.random.randint(0, 29, n_samples),
    'temps_par_client_minutes': np.random.uniform(1, 15, n_samples),
    'productivite_moyenne': np.random.randint(50, 100, n_samples),
    'cout_horaire': np.random.uniform(10, 30, n_samples),
    'formation_requise_jours': np.random.randint(1, 30, n_samples),
    'satisfaction_client': np.random.choice(['moyen', 'eleve'], n_samples, p=[0.3, 0.7])
}

# Create DataFrame
df = pd.DataFrame(data)

# Create target variable with some correlation to the features
base_satisfaction = 70 + 0.2 * df['productivite_moyenne']
experience_effect = np.zeros(n_samples)
experience_effect[df['niveau_experience'] == 'debutant'] = -5
experience_effect[df['niveau_experience'] == 'intermediaire'] = 0
experience_effect[df['niveau_experience'] == 'avance'] = 5
experience_effect[df['niveau_experience'] == 'expert'] = 10

hours_effect = -0.5 * (df['heures_travail_jour'] - 7.5)**2 + 5  # Optimal around 7.5 hours
cost_effect = 0.3 * df['cout_horaire']
client_effect = -0.1 * df['temps_par_client_minutes']

df['satisfaction_client_performance'] = (base_satisfaction + 
                                        experience_effect + 
                                        hours_effect + 
                                        cost_effect + 
                                        client_effect + 
                                        np.random.normal(0, 3, n_samples)).astype(int)

# Ensure the values are within a reasonable range (70-95)
df['satisfaction_client_performance'] = df['satisfaction_client_performance'].clip(70, 95)

# Create a simple apercu_donnees image
plt.figure(figsize=(10, 6))
plt.text(0.5, 0.5, "Aperçu des Données", fontsize=24, ha='center')
plt.axis('off')
plt.savefig('apercu_donnees.png', dpi=300)
plt.close()

# Prepare data for clustering
X = df.drop('satisfaction_client_performance', axis=1)

# Encode categorical variables
X_encoded = pd.get_dummies(X, drop_first=True)

# Standardize data
scaler = StandardScaler()
X_scaled = scaler.fit_transform(X_encoded)

# Apply K-means clustering
kmeans = KMeans(n_clusters=2, random_state=42)
clusters = kmeans.fit_predict(X_scaled)

# Add cluster information to the original dataframe
df['cluster'] = clusters

# Create a PCA projection for visualization
pca = PCA(n_components=2)
X_pca = pca.fit_transform(X_scaled)

# Create K-means clusters visualization
plt.figure(figsize=(10, 8))
plt.scatter(X_pca[:, 0], X_pca[:, 1], c=clusters, cmap='viridis', alpha=0.5)
plt.title('Visualisation des Clusters K-means (Projection PCA)', fontsize=16)
plt.xlabel('Composante Principale 1', fontsize=14)
plt.ylabel('Composante Principale 2', fontsize=14)
plt.colorbar(label='Cluster')
plt.tight_layout()
plt.savefig('kmeans_clusters.png', dpi=300)
plt.close()

# Create boxplots by cluster
numeric_vars = ['heures_travail_jour', 'clients_par_heure', 'temps_par_client_minutes', 
               'productivite_moyenne', 'cout_horaire', 'satisfaction_client_performance']

plt.figure(figsize=(14, 10))
for i, var in enumerate(numeric_vars):
    plt.subplot(2, 3, i+1)
    sns.boxplot(x='cluster', y=var, data=df)
    plt.title(f'Distribution de {var} par Cluster')
plt.tight_layout()
plt.savefig('boxplots_clusters.png', dpi=300)
plt.close()

# Create barplots for categorical variables by cluster
categorical_vars = ['poste', 'niveau_experience', 'satisfaction_client']

fig, axes = plt.subplots(len(categorical_vars), 1, figsize=(12, 15))

for i, var in enumerate(categorical_vars):
    # Calculate counts
    counts = pd.crosstab(df[var], df['cluster'])
    # Convert to percentages
    counts_pct = counts.div(counts.sum(axis=0), axis=1) * 100
    
    # Plot
    counts_pct.plot(kind='bar', ax=axes[i], stacked=False)
    axes[i].set_title(f'Distribution de {var} par Cluster', fontsize=14)
    axes[i].set_ylabel('Pourcentage (%)', fontsize=12)
    axes[i].set_xlabel(var, fontsize=12)
    axes[i].legend(['Cluster 0', 'Cluster 1'])

plt.tight_layout()
plt.savefig('barplots_clusters.png', dpi=300)
plt.close()

print("Images de clustering créées avec succès.")
