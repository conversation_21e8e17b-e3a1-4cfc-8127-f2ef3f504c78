import sys
from sklearn.model_selection import RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.discriminant_analysis import LinearDiscriminantAnalysis
from evaluationModels.evaluation_classification import ClassifierEvaluator
from scipy.stats import randint as sp_randint


class Method_LDA_Classifier:
    def _init_(self):
        self.best_lda = None

    
    def train_lda(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        
        print("Veuillez patienter quelques instants...")

        lda = LinearDiscriminantAnalysis()
        param_dist = {
            'solver': ['svd', 'lsqr', 'eigen'],
            'shrinkage': ['auto', None],
            'n_components': [None, 2, 3, 4, 5]
        }

        random_search = RandomizedSearchCV(lda, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)

        self.best_lda = random_search.best_estimator_
        print(f"Le modèle LDA a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")

        return self

    def predict(self, X_test):
        if self.best_lda is None:
            raise ValueError("Le modèle n'a pas été entraîné. Veuillez appeler la méthode 'train_lda' d'abord.")
        else:
            print("La prédiction avec les données de test...")

        return self.best_lda.predict(X_test)

    def run_lda_classifier(self, X_train, y_train, X_test, y_test):
        print("_Entraînement du modèle LDA_")
        # Entraînement du modèle
        self.train_lda(X_train, y_train)

        # Prédiction sur les données de test
        y_pred = self.predict(X_test)

        print('Evaluation Metrics')
        # Évaluation du modèle
        evaluator = ClassifierEvaluator(y_test, y_pred) # Assurez-vous que cette classe est définie ailleurs
        evaluator.evaluation_metrics()
        
