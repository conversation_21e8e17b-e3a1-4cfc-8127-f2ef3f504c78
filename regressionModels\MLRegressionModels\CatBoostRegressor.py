from catboost import CatBoostRegressor
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import mean_squared_error, r2_score
from scipy.stats import randint, loguniform
from evaluationModels.evaluation_regressor import RegressionEvaluator 

class Method_Catboost_Regressor:
    def __init__(self):
        self.best_parameter = None

    def train_catboost(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'iterations': randint(100, 1001),
            'depth': randint(4, 11),
            'learning_rate': loguniform(0.01, 0.2),
            'l2_leaf_reg': loguniform(1, 10),
            'random_strength': randint(1, 4),
            'bagging_temperature': [0, 0.5, 1, 2]
        }

        model = CatBoostRegressor(
            loss_function='RMSE',
            random_seed=random_state,
            verbose=0
        )

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring='neg_root_mean_squared_error',
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée (RMSE -): {random_search.best_score_:.4f}")

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_catboost_regressor(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle CatBoost______________")
        self.train_catboost(X_train, y_train)
        y_pred = self.predict(X_test)
        print('_________________Évaluation du modèle_________________')
        evaluator = RegressionEvaluator(y_test, y_pred) # Assurez-vous que cette classe est définie ailleurs
        evaluator.evaluation_metrics()
