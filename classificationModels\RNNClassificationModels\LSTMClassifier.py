from keras_tuner import HyperModel, RandomSearch
from keras.models import Sequential
from keras.layers import <PERSON>ST<PERSON>, <PERSON><PERSON>, Flatten
from keras.optimizers import <PERSON>
from evaluationModels.evaluation_classification import ClassifierEvaluator
import numpy as np
import os

class Method_LSTM_Classifier(HyperModel):
    def __init__(self):
        self.best_parameter = None
        self.explainer = None
        self.X_train_summary = None

    def build(self, hp):
        model = Sequential()

        # Première couche LSTM
        model.add(LSTM(units=hp.Int('units', 32, 128, step=32),
                       activation='relu',
                       input_shape=self.X_train_summary,
                       return_sequences=True))

        # Couches LSTM supplémentaires
        num_layers = hp.Int('num_layers', 1, 3)
        for i in range(num_layers):
            model.add(LSTM(units=hp.Int(f'units_{i}', 32, 128, step=32),
                           activation='relu',
                           return_sequences=(i < num_layers - 1)))

        model.add(Flatten())
        model.add(<PERSON><PERSON>(units=hp.Int('dense_units', 64, 256, step=64), activation='relu'))
        model.add(Dense(1, activation='sigmoid'))  # Pour classification binaire

        model.compile(optimizer=Adam(
                          learning_rate=hp.Float('learning_rate', 1e-4, 1e-2, sampling='LOG')),
                      loss='binary_crossentropy',  # Pour classification binaire
                      metrics=['accuracy'])
        return model

    def _get_unique_filename(self, base_name):
        if not os.path.exists(base_name):
            return base_name
        name, ext = os.path.splitext(base_name)
        i = 1
        while os.path.exists(f"{name}_{i}{ext}"):
            i += 1
        return f"{name}_{i}{ext}"

    def train_lstm(self, X_train, y_train, X_test, y_test, n_iter=10, random_state=42):
        print("Recherche des meilleurs hyperparamètres...")

        self.X_train_summary = (X_train.shape[1], X_train.shape[2])

        tuner = RandomSearch(
            self,
            objective='val_loss',
            max_trials=n_iter,
            executions_per_trial=3,
            overwrite=True,
            directory='lstm_classifier_tuning',
            project_name='classification'
        )

        tuner.search(X_train, y_train, epochs=10, validation_data=(X_test, y_test))

        self.best_parameter = tuner.get_best_models(num_models=1)[0]
        
        # Sauvegarde du meilleur modèle
        model_filename = self._get_unique_filename("best_lstm_classifier.h5")
        self.best_parameter.save(model_filename)
        print(f"Meilleur modèle sauvegardé sous : {model_filename}")

        print(f"Meilleurs hyperparamètres : {tuner.get_best_hyperparameters()[0].values}")

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")

        predictions = self.best_parameter.predict(X_test)

        # Pour classification binaire
        predictions = (predictions > 0.5).astype(int).flatten()
        return predictions

    def run_lstm_classifier(self, X_train, y_train, X_test, y_test, n_iter=10):
        print("______________Entraînement du modèle LSTM______________")
        self.train_lstm(X_train, y_train, X_test, y_test, n_iter=n_iter)

        y_pred = self.predict(X_test)

        print('_________________Évaluation des performances_________________')
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
        evaluatorgenerate_pdf_report()
