import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from sklearn.metrics import silhouette_score
from sklearn.decomposition import PCA
from pyclustering.cluster.clarans import clarans
from pyclustering.utils.metric import distance_metric, type_metric
from pyclustering.utils import timedcall


class CLARANSClustering:
    def __init__(self, data, max_clusters=10):
        # Force la conversion en ndarray si c’est un DataFrame
        if isinstance(data, pd.DataFrame):
            self.data = data.values
        else:
            self.data = np.array(data)
        self.max_clusters = max_clusters
        self.best_k = None
        self.labels = None
        self.clusters = None
        self.medoids = None

    def _find_optimal_k(self):
        silhouette_scores = []
        best_score = -1
        for k in range(2, self.max_clusters + 1):
            instance = clarans(self.data.tolist(), k, 1, 5)
            _, _ = timedcall(instance.process)
            clusters = instance.get_clusters()
            labels = [None] * len(self.data)
            for cluster_idx, indices in enumerate(clusters):
                for i in indices:
                    labels[i] = cluster_idx
            score = silhouette_score(self.data, labels)
            silhouette_scores.append(score)
            if score > best_score:
                best_score = score
                self.best_k = k
        self._plot_silhouette(silhouette_scores)
        print(f"Meilleur k : {self.best_k} | Score silhouette : {best_score:.4f}")

    def _fit_model(self):
        instance = clarans(self.data.tolist(), self.best_k, 1, 5)
        _, _ = timedcall(instance.process)
        self.clusters = instance.get_clusters()
        self.medoids = instance.get_medoids()
        self.labels = [None] * len(self.data)
        for cluster_idx, indices in enumerate(self.clusters):
            for i in indices:
                self.labels[i] = cluster_idx

    def _describe_clusters(self):
        print("\n Description des clusters :")
        for label in range(self.best_k):
            count = np.sum(np.array(self.labels) == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        pca = PCA(n_components=2)
        X_pca = pca.fit_transform(self.data)
        medoid_coords = pca.transform([self.data[m, :] for m in self.medoids])

        plt.figure(figsize=(8, 6))
        for i in range(self.best_k):
            points = X_pca[np.array(self.labels) == i]
            plt.scatter(points[:, 0], points[:, 1], label=f"Cluster {i}", alpha=0.5)
        plt.scatter(medoid_coords[:, 0], medoid_coords[:, 1], c='black', marker='X', s=200, label='Médoïdes')
        plt.title("Clustering CLARANS (PCA)")
        plt.legend()
        plt.grid(True)
        plt.show()

    def _plot_silhouette(self, scores):
        plt.plot(range(2, self.max_clusters + 1), scores, marker='o')
        plt.title("Scores de silhouette")
        plt.xlabel("k")
        plt.ylabel("Score")
        plt.grid(True)
        plt.show()

    def run_clarans_clustering(self):
        self._find_optimal_k()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
