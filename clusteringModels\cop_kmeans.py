import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

from sklearn.cluster import KMeans
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import euclidean_distances
import networkx as nx

class COPKMeansClustering:
    def __init__(self, data, must_link=None, cannot_link=None, max_clusters=10, max_attempts=100):
        """
        Initialisez l'algorithme COP-KMeans avec des données et des contraintes.

        Args:
            data: DataFrame ou array numpy contenant les données
            must_link : Liste de paires d'indices qui doivent être dans le même cluster
            can_link: Liste de paires d'indices qui doivent être dans des clusters différents
            max_clusters: Nombre maximum de clusters à essayer
            max_attempts: Nombre maximum de tentatives pour chaque valeur de k
        """
        self.data = data.values if isinstance(data, pd.DataFrame) else data
        self.must_link = must_link if must_link is not None else []
        self.cannot_link = cannot_link if cannot_link is not None else []
        self.max_clusters = max_clusters
        self.max_attempts = max_attempts
        self.best_k = None
        self.model = None
        self.labels = None
        self.centroids = None

    def _find_optimal_k(self):
        """Trouve le nombre optimal de clusters en utilisant la silhouette"""
        best_score = -1
        silhouette_scores = []
        k_values = []

        # Vérifie s'il y a des contraintes qui sont potentiellement incompatibles
        if not self._check_constraints_compatibility():
            print("Attention: Certaines contraintes peuvent être incompatibles entre elles.")

        for k in range(2, self.max_clusters + 1):
            model = self._fit_with_constraints(k)
            if model is not None:
                score = silhouette_score(self.data, model['labels'])
                silhouette_scores.append(score)
                k_values.append(k)
                if score > best_score:
                    best_score = score
                    self.best_k = k

        if silhouette_scores:
            self._plot_silhouette(k_values, silhouette_scores)
            print(f"Meilleur k : {self.best_k} | Score silhouette : {best_score:.4f}")
        else:
            print("Aucune solution ne respecte les contraintes. Essayez de :")
            print("1. Réduire le nombre de contraintes")
            print("2. Modifier les contraintes existantes")
            print("3. Augmenter le nombre d'essais (max_attempts)")
            print("4. Lancer avec moins de contraintes ou sans contraintes")
            raise ValueError("Aucune solution de clustering ne respecte les contraintes fournies.")

    def _check_constraints_compatibility(self):
        """Vérifie si les contraintes sont potentiellement compatibles"""
        # Si pas de contraintes, retourne True
        if not self.must_link and not self.cannot_link:
            return True

        # Crée un graphe pour les contraintes must-link
        ml_graph = nx.Graph()
        for i, j in self.must_link:
            ml_graph.add_edge(i, j)

        # Trouve les composantes connexes (points qui doivent être ensemble)
        ml_components = list(nx.connected_components(ml_graph))

        # Vérifie s'il y a des cannot-link entre des points qui doivent être ensemble
        for comp in ml_components:
            comp = list(comp)
            for i, j in self.cannot_link:
                if i in comp and j in comp:
                    return False  # Contraintes contradictoires détectées

        return True  # Pas de contradiction évidente

    def _fit_with_constraints(self, k):
        """
        Essaie de trouver une solution de clustering qui respecte les contraintes.

        Args:
            k: Nombre de clusters

        Returns:
            Un dictionnaire contenant le modèle, les labels et les centroides si une solution est trouvée,
            None sinon.
        """
        for _ in range(self.max_attempts):
            model = KMeans(n_clusters=k, n_init=1, random_state=None)
            model.fit(self.data)
            labels = model.labels_

            if self._respect_constraints(labels):
                return {'model': model, 'labels': labels, 'centroids': model.cluster_centers_}

        return None

    def _respect_constraints(self, labels):
        """
        Vérifie si les labels respectent les contraintes.

        Args:
            labels: Les labels à vérifier

        Returns:
            True si les contraintes sont respectées, False sinon
        """
        for i, j in self.must_link:
            if labels[i] != labels[j]:
                return False
        for i, j in self.cannot_link:
            if labels[i] == labels[j]:
                return False
        return True

    def _fit_model(self):
        """Applique le modèle avec le meilleur k"""
        result = self._fit_with_constraints(self.best_k)
        if result is not None:
            self.model = result['model']
            self.labels = result['labels']
            self.centroids = result['centroids']
        else:
            raise ValueError(f"Impossible de trouver une solution avec k={self.best_k}")

    def _describe_clusters(self):
        """Affiche des informations sur les clusters"""
        print("\nDescription des clusters :")
        for label in range(self.best_k):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        """Visualise les clusters selon la dimension des données"""
        dims = self.data.shape[1]
        data = self.data
        labels = self.labels
        centroids = self.centroids

        if dims == 2:
            X1, X2 = data[:, 0], data[:, 1]
            distances = euclidean_distances(data, centroids)
            radius = [np.max(distances[labels == i, i]) for i in range(len(centroids))]
            plt.figure(figsize=(10, 8))
            plt.scatter(X1, X2, c=labels, s=40, cmap='viridis')
            plt.scatter(centroids[:, 0], centroids[:, 1], c='red', marker='x', s=100, label='Centroides')
            for i, centroid in enumerate(centroids):
                circle = plt.Circle(centroid, radius[i], color='black', fill=False)
                plt.gca().add_patch(circle)
            plt.title("Clustering COP-KMeans (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.legend()
            plt.grid(True)
            plt.show()
        elif dims == 3:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(data[:, 0], data[:, 1], data[:, 2], c=labels, s=40, cmap='viridis')
            ax.scatter(centroids[:, 0], centroids[:, 1], centroids[:, 2], c='black', s=100, label='Centroides')
            ax.set_title("Clustering COP-KMeans (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.legend()
            plt.show()
        else:
            print("Visualisation uniquement disponible pour 2D ou 3D.")

            # Si plus de 3 dimensions, on peut afficher les 2 premières composantes principales
            from sklearn.decomposition import PCA
            pca = PCA(n_components=2)
            X_pca = pca.fit_transform(data)

            plt.figure(figsize=(10, 8))
            plt.scatter(X_pca[:, 0], X_pca[:, 1], c=labels, s=40, cmap='viridis')
            plt.title("Projection PCA du Clustering COP-KMeans")
            plt.xlabel("Composante principale 1")
            plt.ylabel("Composante principale 2")
            plt.grid(True)
            plt.show()

    def _plot_silhouette(self, k_values, scores):
        """Affiche le graphique des scores de silhouette"""
        plt.figure(figsize=(10, 6))
        plt.plot(k_values, scores, marker='o', linestyle='-', color='blue')
        plt.title("Scores de silhouette par nombre de clusters")
        plt.xlabel("Nombre de clusters (k)")
        plt.ylabel("Score de silhouette")
        plt.grid(True)
        plt.show()

    def lancer_cop_kmeans_clustering(self):
        """
        Exécute l'algorithme COP-KMeans complet :
        1. Trouve le k optimal
        2. Applique le modèle
        3. Décrit les clusters
        4. Visualise les résultats
        """
        try:
            self._find_optimal_k()
            self._fit_model()
            self._describe_clusters()
            self._visualize()
            return True
        except ValueError as e:
            print(f"Erreur: {e}")
            return False

    def run_cop_kmeans_clustering(self):
        """
        Fonction principale pour lancer le clustering avec choix utilisateur
        """

        print("="*50)
        print("CLUSTERING COP-KMEANS AVEC CONTRAINTES")
        print("="*50)

        # Demander à l'utilisateur de choisir
        print("\nVoulez-vous utiliser:")
        print("1. Les paramètres par défaut")
        print("2. Définir vos propres paramètres")

        choice = input("Votre choix (1 ou 2): ")

        if choice == "1":
            # Paramètres par défaut
            must_link = []
            cannot_link = []
            max_clusters = 10
            max_attempts = 100

            print("\nParamètres par défaut:")
            print("- Aucune contrainte must-link")
            print("- Aucune contrainte cannot-link")
            print("- Maximum 10 clusters")
            print("- 100 tentatives par valeur de k")

        elif choice == "2":
            # Paramètres personnalisés
            print("\nDéfinissez vos contraintes must-link (paires d'indices qui doivent être dans le même cluster)")
            print("Format: i,j (exemple: 0,1) - Appuyez sur Entrée sans rien saisir pour terminer")

            must_link = []

            pair = input("Paire must-link (ou Entrée pour terminer): ")

            try:
                    i, j = map(int, pair.split(','))
                    must_link.append([i, j])
            except:
                    print("Format invalide. Utilisez i,j (exemple: 0,1)")

            print("\nDéfinissez vos contraintes cannot-link (paires d'indices qui doivent être dans des clusters différents)")
            cannot_link = []

            pair = input("Paire cannot-link (ou Entrée pour terminer): ")

            try:
                i, j = map(int, pair.split(','))
                cannot_link.append([i, j])
            except:
                print("Format invalide. Utilisez i,j (exemple: 0,1)")

            try:
                max_clusters = int(input("\nNombre maximum de clusters à essayer (2-20): "))
                max_clusters = max(2, min(20, max_clusters))  # Limiter entre 2 et 20
            except:
                max_clusters = 10
                print("Valeur invalide. Utilisation de 10 clusters maximum.")

            try:
                max_attempts = int(input("\nNombre maximum de tentatives par valeur de k (10-1000): "))
                max_attempts = max(10, min(1000, max_attempts))  # Limiter entre 10 et 1000
            except:
                max_attempts = 100
                print("Valeur invalide. Utilisation de 100 tentatives.")
        else:
            print("Choix invalide. Utilisation des paramètres par défaut.")

            must_link = [],
            cannot_link = [],
            max_clusters = 10,
            max_attempts = 100

        # Créer et exécuter le modèle
        print("\nLancement du clustering...")
        clustering_model = COPKMeansClustering(
            self.data,
            must_link=must_link,
            cannot_link=cannot_link,
            max_clusters=max_clusters,
            max_attempts=max_attempts
        )

        success = clustering_model.lancer_cop_kmeans_clustering()

        return clustering_model if success else None
        