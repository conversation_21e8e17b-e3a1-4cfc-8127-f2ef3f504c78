import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import accuracy_score, recall_score, precision_score, f1_score, confusion_matrix, classification_report
from reportlab.lib.pagesizes import letter
from reportlab.pdfgen import canvas
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer
from reportlab.lib.styles import getSampleStyleSheet
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Image


class ClassifierEvaluator:
    def __init__(self, y_true, y_pred, y_scores=None):
        # Vérification de la compatibilité des cibles et des prédictions
        if len(y_true) != len(y_pred):
            raise ValueError("Les longueurs de y_true et y_pred ne sont pas égales.")
        self.y_pred = y_pred
        self.y_true = y_true

        # Affichage des classes uniques
        print(f"y_true: {np.unique(self.y_true)}")
        print(f"y_pred: {np.unique(self.y_pred)}")
        
        self.y_scores = y_scores
        self.classification_type = self.determine_classification_type()

    def determine_classification_type(self):
        """Détermine si la classification est binaire ou multiclasses en fonction des classes uniques."""
        unique_classes = np.unique(self.y_true)
        if len(unique_classes) == 2:
            return 'binary'
        else:
            return 'multiclass'

    def calculate_accuracy(self):
        return accuracy_score(self.y_true, self.y_pred)

    def calculate_precision(self):
        return precision_score(self.y_true, self.y_pred, average='weighted')

    def calculate_recall(self):
        return recall_score(self.y_true, self.y_pred, average='weighted')

    def calculate_f1_score(self):
        return f1_score(self.y_true, self.y_pred, average='weighted')

    def get_confusion_matrix(self):
        cm = confusion_matrix(self.y_true, self.y_pred)
        
        # Tracer la matrice de confusion sans sauvegarde
        plt.figure(figsize=(8, 6))
        sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', cbar=False)
        plt.title('Matrice de confusion')
        plt.xlabel('Prédit')
        plt.ylabel('Vrai')
        plt.tight_layout()
        plt.show() 
    
    def evaluation_metrics(self):
        """Affiche les métriques d'évaluation et la matrice de confusion."""
        accuracy = self.calculate_accuracy()
        precision = self.calculate_precision()
        recall = self.calculate_recall()
        f1 = self.calculate_f1_score()
        class_report = classification_report(self.y_true, self.y_pred)

        # Affichage
        print(f"Accuracy: {accuracy:.2%}")
        print(f"Precision: {precision:.2%}")
        print(f"Recall: {recall:.2%}")
        print(f"F1 Score: {f1:.2%}")
        print("Classification Report:")
        print(class_report)
        
        self.get_confusion_matrix()

