import os
from keras_tuner import HyperModel, RandomSearch
from keras.models import Sequential
from keras.layers import GR<PERSON>, Dense, Dropout, Flatten
from keras.optimizers import Adam
from evaluationModels.evaluation_classification import ClassifierEvaluator
import numpy as np

class Method_GRU_Classifier(HyperModel):
    def __init__(self):
        self.best_parameter = None
        self.X_train_summary = None

    def build(self, hp):
        model = Sequential()

        # Première couche GRU
        model.add(GRU(units=hp.Int('units_first', 32, 128, step=32),
                      activation='tanh',
                      input_shape=self.X_train_summary,
                      return_sequences=True))
        model.add(Dropout(rate=hp.Float('dropout_first', 0.1, 0.5, step=0.1)))

        # Couches GRU supplémentaires
        num_layers = hp.Int('num_layers', 1, 3)
        for i in range(num_layers):
            return_seq = i < num_layers - 1
            model.add(GRU(units=hp.Int(f'units_{i}', 32, 128, step=32),
                          activation='tanh',
                          return_sequences=return_seq))
            model.add(Dropout(rate=hp.Float(f'dropout_{i}', 0.1, 0.5, step=0.1)))

        model.add(Flatten())
        model.add(Dense(units=hp.Int('dense_units', 64, 256, step=64), activation='relu'))
        model.add(Dense(1, activation='sigmoid'))  # Pour classification binaire

        model.compile(optimizer=Adam(
                          learning_rate=hp.Float('learning_rate', 1e-4, 1e-2, sampling='LOG')),
                      loss='binary_crossentropy',
                      metrics=['accuracy'])
        return model

    def _get_unique_filename(self, base_name):
        if not os.path.exists(base_name):
            return base_name
        name, ext = os.path.splitext(base_name)
        i = 1
        while os.path.exists(f"{name}_{i}{ext}"):
            i += 1
        return f"{name}_{i}{ext}"

    def train_gru(self, X_train, y_train, X_test, y_test, n_iter=10, epochs=10):
        self.X_train_summary = (X_train.shape[1], X_train.shape[2])

        tuner = RandomSearch(
            self,
            objective='val_loss',
            max_trials=n_iter,
            executions_per_trial=2,
            overwrite=True,
            directory='gru_classifier_tuning',
            project_name='gru_classification'
        )

        print("Recherche des meilleurs hyperparamètres GRU (classification)...")
        tuner.search(X_train, y_train, epochs=epochs, validation_data=(X_test, y_test))
        tuner.results_summary()

        best_model = tuner.get_best_models(1)[0]
        model_filename = self._get_unique_filename("best_gru_classifier.h5")
        best_model.save(model_filename)
        print(f"Meilleur modèle GRU classification sauvegardé sous : {model_filename}")

        self.best_parameter = best_model
        print(f"Meilleurs hyperparamètres GRU : {tuner.get_best_hyperparameters()[0].values}")

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle GRU n'a pas été entraîné.")
        y_prob = self.best_parameter.predict(X_test)
        return (y_prob > 0.5).astype(int).flatten()  # Seuil de 0.5 pour classification binaire

    def run_gru_classifier(self, X_train, y_train, X_test, y_test, n_iter=10, epochs=10):
        print("______________Entraînement du modèle GRU (classification)______________")
        self.train_gru(X_train, y_train, X_test, y_test, n_iter=n_iter, epochs=epochs)
        y_pred = self.predict(X_test)

        print("_________________Évaluation des performances_________________")
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
        evaluator.generate_pdf_report()
