import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import (
    mean_squared_error,
    mean_absolute_error,
    r2_score,
    explained_variance_score
)
class RegressionEvaluator:
    """
    Classe pour évaluer les performances des modèles de régression.
    """
    def __init__(self, y_true, y_pred):
        self.y_true = y_true
        self.y_pred = y_pred

    # def calculate_mae(self):
    #     """Calcule le Mean Absolute Error (MAE)."""
    #     return mean_absolute_error(self.y_true, self.y_pred)

    # def calculate_mse(self):
    #     """Calcule le Mean Squared Error (MSE)."""
    #     return mean_squared_error(self.y_true, self.y_pred)

    # def calculate_rmse(self):
    #     """Calcule le Root Mean Squared Error (RMSE)."""
    #     return np.sqrt(mean_squared_error(self.y_true, self.y_pred))

    # def calculate_r2_score(self):
    #     """Calcule le coefficient de détermination R²."""
    #     return r2_score(self.y_true, self.y_pred)

    # def evaluation_metrics(self):
    #     """Affiche toutes les métriques d'évaluation pour la régression."""
    #     print(f"Mean Absolute Error (MAE): {self.calculate_mae():.4f}")
    #     print(f"Mean Squared Error (MSE): {self.calculate_mse():.4f}")
    #     print(f"Root Mean Squared Error (RMSE): {self.calculate_rmse():.4f}")
    #     print(f"Coefficient of Determination (R²): {self.calculate_r2_score():.4f}")

    def evaluation_metrics(self):
            print("----- Rapport d'Évaluation de Régression -----")

            # Calculer les métriques
            mse = mean_squared_error(self.y_true, self.y_pred)
            rmse = np.sqrt(mse)
            mae = mean_absolute_error(self.y_true, self.y_pred)
            r2 = r2_score(self.y_true, self.y_pred)
            evs = explained_variance_score(self.y_true, self.y_pred)

            # Afficher les métriques
            print(f"Erreur quadratique moyenne (MSE)      : {mse:.4f}")
            print(f"Racine de l'erreur quadratique (RMSE) : {rmse:.4f}")
            print(f"Erreur absolue moyenne (MAE)          : {mae:.4f}")
            print(f"Coefficient de détermination (R²)     : {r2:.4f}")
            print(f"Score de variance expliquée           : {evs:.4f}")

            # Visualiser les prédictions vs valeurs réelles
            plt.figure(figsize=(10, 6))
            plt.scatter(self.y_true, self.y_pred, alpha=0.7)
            plt.plot([min(self.y_true), max(self.y_true)], [min(self.y_true), max(self.y_true)], 'r--')
            plt.xlabel('Valeurs réelles')
            plt.ylabel('Prédictions')
            plt.title('Prédictions vs Valeurs réelles')
            plt.grid(True)
            plt.show()

            # Visualiser la distribution des erreurs
            errors = self.y_pred - self.y_true
            plt.figure(figsize=(10, 6))
            sns.histplot(errors, kde=True)
            plt.xlabel('Erreur de prédiction')
            plt.ylabel('Fréquence')
            plt.title('Distribution des erreurs de prédiction')
            plt.grid(True)
            plt.show()

