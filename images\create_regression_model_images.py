import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.neighbors import KNeighborsRegressor
from sklearn.tree import <PERSON><PERSON>reeRegressor, plot_tree
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_squared_error, r2_score

# Set the style for the plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("viridis")

# Create a synthetic dataset similar to the one described
np.random.seed(42)

# Number of samples
n_samples = 10000

# Generate synthetic data
data = {
    'poste': np.random.choice(['caissier', 'serveur', 'barista', 'nettoyeur', 'cuisinier', 'manager', 'receptionniste'], n_samples, p=[0.25, 0.3, 0.15, 0.1, 0.1, 0.05, 0.05]),
    'niveau_experience': np.random.choice(['debutant', 'intermediaire', 'avance', 'expert'], n_samples, p=[0.2, 0.4, 0.3, 0.1]),
    'heures_travail_jour': np.random.uniform(5, 10, n_samples),
    'clients_par_heure': np.random.randint(0, 29, n_samples),
    'temps_par_client_minutes': np.random.uniform(1, 15, n_samples),
    'productivite_moyenne': np.random.randint(50, 100, n_samples),
    'cout_horaire': np.random.uniform(10, 30, n_samples),
    'formation_requise_jours': np.random.randint(1, 30, n_samples),
    'satisfaction_client': np.random.choice(['moyen', 'eleve'], n_samples, p=[0.3, 0.7])
}

# Create DataFrame
df = pd.DataFrame(data)

# Create target variable with some correlation to the features
base_satisfaction = 70 + 0.2 * df['productivite_moyenne']
experience_effect = np.zeros(n_samples)
experience_effect[df['niveau_experience'] == 'debutant'] = -5
experience_effect[df['niveau_experience'] == 'intermediaire'] = 0
experience_effect[df['niveau_experience'] == 'avance'] = 5
experience_effect[df['niveau_experience'] == 'expert'] = 10

hours_effect = -0.5 * (df['heures_travail_jour'] - 7.5)**2 + 5  # Optimal around 7.5 hours
cost_effect = 0.3 * df['cout_horaire']
client_effect = -0.1 * df['temps_par_client_minutes']

df['satisfaction_client_performance'] = (base_satisfaction + 
                                        experience_effect + 
                                        hours_effect + 
                                        cost_effect + 
                                        client_effect + 
                                        np.random.normal(0, 3, n_samples)).astype(int)

# Ensure the values are within a reasonable range (70-95)
df['satisfaction_client_performance'] = df['satisfaction_client_performance'].clip(70, 95)

# Prepare data for modeling
X = df.drop('satisfaction_client_performance', axis=1)
y = df['satisfaction_client_performance']

# Encode categorical variables
X_encoded = pd.get_dummies(X, drop_first=True)

# Split data
X_train, X_test, y_train, y_test = train_test_split(X_encoded, y, test_size=0.2, random_state=42)

# Standardize features
scaler = StandardScaler()
X_train_scaled = scaler.fit_transform(X_train)
X_test_scaled = scaler.transform(X_test)

# Create KNN model
knn = KNeighborsRegressor(n_neighbors=24, metric='manhattan')
knn.fit(X_train_scaled, y_train)
y_pred_knn = knn.predict(X_test_scaled)

# Create Decision Tree model
dt = DecisionTreeRegressor(max_depth=3, min_samples_split=19, min_samples_leaf=4, random_state=42)
dt.fit(X_train, y_train)
y_pred_dt = dt.predict(X_test)

# Create Random Forest model
rf = RandomForestRegressor(n_estimators=113, max_depth=3, min_samples_split=19, min_samples_leaf=4, random_state=42)
rf.fit(X_train, y_train)
y_pred_rf = rf.predict(X_test)

# Create Linear Regression model
lr = LinearRegression()
lr.fit(X_train_scaled, y_train)
y_pred_lr = lr.predict(X_test_scaled)

# Create KNN predictions visualization
plt.figure(figsize=(10, 8))
plt.scatter(y_test, y_pred_knn, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
plt.xlabel('Valeurs Réelles', fontsize=14)
plt.ylabel('Valeurs Prédites', fontsize=14)
plt.title('Comparaison des Valeurs Prédites et Réelles (KNN)', fontsize=16)
plt.tight_layout()
plt.savefig('knn_predictions.png', dpi=300)
plt.close()

# Create Decision Tree predictions visualization
plt.figure(figsize=(10, 8))
plt.scatter(y_test, y_pred_dt, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
plt.xlabel('Valeurs Réelles', fontsize=14)
plt.ylabel('Valeurs Prédites', fontsize=14)
plt.title('Comparaison des Valeurs Prédites et Réelles (Decision Tree)', fontsize=16)
plt.tight_layout()
plt.savefig('decision_tree_predictions.png', dpi=300)
plt.close()

# Create Decision Tree structure visualization
plt.figure(figsize=(20, 10))
plot_tree(dt, filled=True, feature_names=X_train.columns, rounded=True, fontsize=10)
plt.title('Structure de l\'Arbre de Décision (profondeur 3)', fontsize=16)
plt.tight_layout()
plt.savefig('decision_tree_structure.png', dpi=300)
plt.close()

# Create Random Forest predictions visualization
plt.figure(figsize=(10, 8))
plt.scatter(y_test, y_pred_rf, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
plt.xlabel('Valeurs Réelles', fontsize=14)
plt.ylabel('Valeurs Prédites', fontsize=14)
plt.title('Comparaison des Valeurs Prédites et Réelles (Random Forest)', fontsize=16)
plt.tight_layout()
plt.savefig('random_forest_predictions.png', dpi=300)
plt.close()

# Create Random Forest feature importance visualization
feature_importance = pd.Series(rf.feature_importances_, index=X_train.columns).sort_values(ascending=False)
plt.figure(figsize=(12, 8))
feature_importance[:10].plot(kind='bar')
plt.title('Importance des Variables dans le Modèle Random Forest', fontsize=16)
plt.ylabel('Importance Relative', fontsize=14)
plt.tight_layout()
plt.savefig('random_forest_feature_importance.png', dpi=300)
plt.close()

# Create Linear Regression predictions visualization
plt.figure(figsize=(10, 8))
plt.scatter(y_test, y_pred_lr, alpha=0.5)
plt.plot([y_test.min(), y_test.max()], [y_test.min(), y_test.max()], 'k--', lw=2)
plt.xlabel('Valeurs Réelles', fontsize=14)
plt.ylabel('Valeurs Prédites', fontsize=14)
plt.title('Comparaison des Valeurs Prédites et Réelles (Régression Linéaire)', fontsize=16)
plt.tight_layout()
plt.savefig('linear_regression_predictions.png', dpi=300)
plt.close()

# Create Linear Regression coefficients visualization
coefficients = pd.Series(lr.coef_, index=X_train.columns)
top_coeffs = coefficients.abs().sort_values(ascending=False).head(10).index
plt.figure(figsize=(12, 8))
coefficients[top_coeffs].sort_values().plot(kind='barh')
plt.title('Coefficients du Modèle de Régression Linéaire (Top 10)', fontsize=16)
plt.xlabel('Coefficient', fontsize=14)
plt.tight_layout()
plt.savefig('linear_regression_coefficients.png', dpi=300)
plt.close()

print("Images des modèles de régression créées avec succès.")
