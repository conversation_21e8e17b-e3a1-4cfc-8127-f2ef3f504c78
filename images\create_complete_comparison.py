import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns

# Set the style for the plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("viridis")

# Data for all models
models = ['Random Forest', 'Extra Trees', 'XGBoost', 'Decision Tree', 'Linear Regression', 'Gradient Boosting', 'KNN']
r2_scores = [0.7911, 0.7893, 0.7889, 0.7908, 0.7899, 0.7858, 0.7831]
mse_scores = [0.2123, 0.2142, 0.2146, 0.2126, 0.2136, 0.2177, 0.2205]
rmse_scores = [0.4608, 0.4628, 0.4632, 0.4611, 0.4621, 0.4666, 0.4696]

# Sort by R² score (descending)
sorted_indices = np.argsort(r2_scores)[::-1]
models_sorted = [models[i] for i in sorted_indices]
r2_sorted = [r2_scores[i] for i in sorted_indices]

# Create R² comparison chart
plt.figure(figsize=(14, 8))
bars = plt.bar(models_sorted, r2_sorted, color=['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2'])

# Add value labels on bars
for i, (bar, score) in enumerate(zip(bars, r2_sorted)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
             f'{score:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=11)

plt.title('Comparaison des Performances des Modèles de Régression (R²)', fontsize=16, fontweight='bold')
plt.ylabel('Coefficient de Détermination (R²)', fontsize=14)
plt.xlabel('Modèles de Régression', fontsize=14)
plt.xticks(rotation=45, ha='right')
plt.ylim(0.78, 0.795)
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('comparaison_modeles_regression_complete.png', dpi=300, bbox_inches='tight')
plt.close()

# Create comprehensive comparison table visualization
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# R² comparison
ax1.bar(models_sorted, r2_sorted, color='skyblue', alpha=0.7)
ax1.set_title('Coefficient de Détermination (R²)', fontsize=14, fontweight='bold')
ax1.set_ylabel('R²', fontsize=12)
ax1.tick_params(axis='x', rotation=45)
for i, v in enumerate(r2_sorted):
    ax1.text(i, v + 0.0005, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# MSE comparison
mse_sorted = [mse_scores[i] for i in sorted_indices]
ax2.bar(models_sorted, mse_sorted, color='lightcoral', alpha=0.7)
ax2.set_title('Erreur Quadratique Moyenne (MSE)', fontsize=14, fontweight='bold')
ax2.set_ylabel('MSE', fontsize=12)
ax2.tick_params(axis='x', rotation=45)
for i, v in enumerate(mse_sorted):
    ax2.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# RMSE comparison
rmse_sorted = [rmse_scores[i] for i in sorted_indices]
ax3.bar(models_sorted, rmse_sorted, color='lightgreen', alpha=0.7)
ax3.set_title('Racine de l\'Erreur Quadratique (RMSE)', fontsize=14, fontweight='bold')
ax3.set_ylabel('RMSE', fontsize=12)
ax3.tick_params(axis='x', rotation=45)
for i, v in enumerate(rmse_sorted):
    ax3.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# Performance ranking
ax4.barh(range(len(models_sorted)), r2_sorted, color='gold', alpha=0.7)
ax4.set_yticks(range(len(models_sorted)))
ax4.set_yticklabels(models_sorted)
ax4.set_title('Classement des Modèles par Performance (R²)', fontsize=14, fontweight='bold')
ax4.set_xlabel('R²', fontsize=12)
for i, v in enumerate(r2_sorted):
    ax4.text(v + 0.0005, i, f'{v:.4f}', ha='left', va='center', fontsize=10)

plt.tight_layout()
plt.savefig('analyse_complete_modeles_regression.png', dpi=300, bbox_inches='tight')
plt.close()

print("Graphiques de comparaison complète créés avec succès!")
print(f"Modèles classés par performance (R²):")
for i, (model, score) in enumerate(zip(models_sorted, r2_sorted), 1):
    print(f"{i}. {model}: {score:.4f}")
