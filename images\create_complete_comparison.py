import matplotlib.pyplot as plt
import numpy as np
import seaborn as sns
import pandas as pd

# Set the style for the plots
plt.style.use('seaborn-v0_8-whitegrid')
sns.set_palette("viridis")

# Data for all models with complete metrics
models = ['Random Forest', 'Extra Trees', 'XGBoost', 'Decision Tree', 'Linear Regression', 'Gradient Boosting', 'KNN']
r2_scores = [0.7911, 0.7893, 0.7889, 0.7908, 0.7899, 0.7858, 0.7831]
mse_scores = [0.2123, 0.2142, 0.2146, 0.2126, 0.2136, 0.2177, 0.2205]
rmse_scores = [0.4608, 0.4628, 0.4632, 0.4611, 0.4621, 0.4666, 0.4696]
mae_scores = [0.3998, 0.4016, 0.4015, 0.4002, 0.4013, 0.4046, 0.4059]

# Sort by R² score (descending)
sorted_indices = np.argsort(r2_scores)[::-1]
models_sorted = [models[i] for i in sorted_indices]
r2_sorted = [r2_scores[i] for i in sorted_indices]
mse_sorted = [mse_scores[i] for i in sorted_indices]
rmse_sorted = [rmse_scores[i] for i in sorted_indices]
mae_sorted = [mae_scores[i] for i in sorted_indices]

# Define colors for each model
colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd', '#8c564b', '#e377c2']

# 1. Main R² comparison chart
plt.figure(figsize=(14, 8))
bars = plt.bar(models_sorted, r2_sorted, color=colors)

# Add value labels on bars
for i, (bar, score) in enumerate(zip(bars, r2_sorted)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001,
             f'{score:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=11)

plt.title('Comparaison des Performances des Modèles de Régression (R²)', fontsize=16, fontweight='bold')
plt.ylabel('Coefficient de Détermination (R²)', fontsize=14)
plt.xlabel('Modèles de Régression', fontsize=14)
plt.xticks(rotation=45, ha='right')
plt.ylim(0.78, 0.795)
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('comparaison_modeles_regression_complete.png', dpi=300, bbox_inches='tight')
plt.close()

# 2. Comprehensive 4-panel comparison
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

# R² comparison
bars1 = ax1.bar(models_sorted, r2_sorted, color='skyblue', alpha=0.8)
ax1.set_title('Coefficient de Détermination (R²)', fontsize=14, fontweight='bold')
ax1.set_ylabel('R²', fontsize=12)
ax1.tick_params(axis='x', rotation=45)
for i, v in enumerate(r2_sorted):
    ax1.text(i, v + 0.0005, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# MSE comparison
bars2 = ax2.bar(models_sorted, mse_sorted, color='lightcoral', alpha=0.8)
ax2.set_title('Erreur Quadratique Moyenne (MSE)', fontsize=14, fontweight='bold')
ax2.set_ylabel('MSE', fontsize=12)
ax2.tick_params(axis='x', rotation=45)
for i, v in enumerate(mse_sorted):
    ax2.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# RMSE comparison
bars3 = ax3.bar(models_sorted, rmse_sorted, color='lightgreen', alpha=0.8)
ax3.set_title('Racine de l\'Erreur Quadratique (RMSE)', fontsize=14, fontweight='bold')
ax3.set_ylabel('RMSE', fontsize=12)
ax3.tick_params(axis='x', rotation=45)
for i, v in enumerate(rmse_sorted):
    ax3.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=10)

# Performance ranking
bars4 = ax4.barh(range(len(models_sorted)), r2_sorted, color='gold', alpha=0.8)
ax4.set_yticks(range(len(models_sorted)))
ax4.set_yticklabels(models_sorted)
ax4.set_title('Classement des Modèles par Performance (R²)', fontsize=14, fontweight='bold')
ax4.set_xlabel('R²', fontsize=12)
for i, v in enumerate(r2_sorted):
    ax4.text(v + 0.0005, i, f'{v:.4f}', ha='left', va='center', fontsize=10)

plt.tight_layout()
plt.savefig('analyse_complete_modeles_regression.png', dpi=300, bbox_inches='tight')
plt.close()

# 3. Detailed metrics comparison (all 4 metrics)
fig, axes = plt.subplots(2, 2, figsize=(16, 12))
metrics = ['R²', 'MSE', 'RMSE', 'MAE']
data = [r2_sorted, mse_sorted, rmse_sorted, mae_sorted]
colors_metrics = ['steelblue', 'crimson', 'forestgreen', 'darkorange']

for i, (ax, metric, values, color) in enumerate(zip(axes.flat, metrics, data, colors_metrics)):
    bars = ax.bar(models_sorted, values, color=color, alpha=0.7)
    ax.set_title(f'{metric} - Comparaison des Modèles', fontsize=14, fontweight='bold')
    ax.set_ylabel(metric, fontsize=12)
    ax.tick_params(axis='x', rotation=45)

    # Add value labels
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.4f}', ha='center', va='bottom', fontsize=10)

plt.tight_layout()
plt.savefig('metriques_detaillees_modeles.png', dpi=300, bbox_inches='tight')
plt.close()

# 4. Radar chart for top 5 models
from math import pi

# Select top 5 models
top_5_models = models_sorted[:5]
top_5_r2 = r2_sorted[:5]
top_5_mse = [1-x for x in mse_sorted[:5]]  # Invert MSE for radar (higher is better)
top_5_rmse = [1-x for x in rmse_sorted[:5]]  # Invert RMSE for radar (higher is better)
top_5_mae = [1-x for x in mae_sorted[:5]]  # Invert MAE for radar (higher is better)

# Number of variables
categories = ['R²', 'MSE (inv)', 'RMSE (inv)', 'MAE (inv)']
N = len(categories)

# Compute angle for each axis
angles = [n / float(N) * 2 * pi for n in range(N)]
angles += angles[:1]

# Create radar chart
fig, ax = plt.subplots(figsize=(12, 10), subplot_kw=dict(projection='polar'))

# Colors for top 5 models
radar_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

for i, model in enumerate(top_5_models):
    values = [top_5_r2[i], top_5_mse[i], top_5_rmse[i], top_5_mae[i]]
    values += values[:1]

    ax.plot(angles, values, 'o-', linewidth=2, label=model, color=radar_colors[i])
    ax.fill(angles, values, alpha=0.25, color=radar_colors[i])

# Add category labels
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories, fontsize=12)
ax.set_ylim(0, 1)
ax.set_title('Comparaison Radar des Top 5 Modèles\n(Toutes Métriques Normalisées)',
             size=16, fontweight='bold', pad=20)
ax.legend(loc='upper right', bbox_to_anchor=(1.3, 1.0))
ax.grid(True)

plt.tight_layout()
plt.savefig('radar_chart_top5_modeles.png', dpi=300, bbox_inches='tight')
plt.close()

# 5. Performance gap analysis
plt.figure(figsize=(14, 8))
x_pos = np.arange(len(models_sorted))
best_r2 = max(r2_sorted)
gaps = [best_r2 - score for score in r2_sorted]

bars = plt.bar(x_pos, gaps, color=['green' if gap == 0 else 'orange' if gap < 0.005 else 'red' for gap in gaps])
plt.xlabel('Modèles de Régression', fontsize=14)
plt.ylabel('Écart par rapport au meilleur modèle', fontsize=14)
plt.title('Analyse de l\'Écart de Performance (R²)', fontsize=16, fontweight='bold')
plt.xticks(x_pos, models_sorted, rotation=45, ha='right')

# Add value labels
for i, (bar, gap) in enumerate(zip(bars, gaps)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0002,
             f'{gap:.4f}', ha='center', va='bottom', fontsize=10)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('analyse_ecart_performance.png', dpi=300, bbox_inches='tight')
plt.close()

# 6. Model efficiency analysis (Performance vs Complexity)
complexity_scores = [8, 7, 9, 3, 1, 6, 2]  # Subjective complexity ranking (1=simple, 10=complex)
complexity_sorted = [complexity_scores[i] for i in sorted_indices]

plt.figure(figsize=(12, 8))
scatter = plt.scatter(complexity_sorted, r2_sorted, s=200, c=range(len(models_sorted)),
                     cmap='viridis', alpha=0.7, edgecolors='black', linewidth=2)

# Add model labels
for i, model in enumerate(models_sorted):
    plt.annotate(model, (complexity_sorted[i], r2_sorted[i]),
                xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')

plt.xlabel('Complexité du Modèle (1=Simple, 10=Complexe)', fontsize=14)
plt.ylabel('Performance (R²)', fontsize=14)
plt.title('Analyse Efficacité: Performance vs Complexité', fontsize=16, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.colorbar(scatter, label='Rang de Performance')
plt.tight_layout()
plt.savefig('performance_vs_complexite.png', dpi=300, bbox_inches='tight')
plt.close()

# 7. Summary statistics table as image
fig, ax = plt.subplots(figsize=(14, 8))
ax.axis('tight')
ax.axis('off')

# Create summary table
summary_data = {
    'Modèle': models_sorted,
    'Rang': list(range(1, len(models_sorted) + 1)),
    'R²': [f'{score:.4f}' for score in r2_sorted],
    'MSE': [f'{score:.4f}' for score in mse_sorted],
    'RMSE': [f'{score:.4f}' for score in rmse_sorted],
    'MAE': [f'{score:.4f}' for score in mae_sorted],
    'Écart vs Meilleur': [f'{gap:.4f}' for gap in gaps]
}

df = pd.DataFrame(summary_data)
table = ax.table(cellText=df.values, colLabels=df.columns, cellLoc='center', loc='center')
table.auto_set_font_size(False)
table.set_fontsize(11)
table.scale(1.2, 2)

# Color code the ranking
for i in range(len(models_sorted)):
    if i == 0:  # Best model
        table[(i+1, 0)].set_facecolor('#90EE90')  # Light green
        table[(i+1, 1)].set_facecolor('#90EE90')
    elif i <= 2:  # Top 3
        table[(i+1, 0)].set_facecolor('#FFE4B5')  # Light orange
        table[(i+1, 1)].set_facecolor('#FFE4B5')

plt.title('Tableau Récapitulatif des Performances - Tous Modèles',
          fontsize=16, fontweight='bold', pad=20)
plt.savefig('tableau_recapitulatif_performances.png', dpi=300, bbox_inches='tight')
plt.close()

print("🎯 Tous les graphiques de comparaison ont été créés avec succès!")
print("\n📊 Images générées:")
print("1. comparaison_modeles_regression_complete.png - Graphique principal R²")
print("2. analyse_complete_modeles_regression.png - Analyse 4 panneaux")
print("3. metriques_detaillees_modeles.png - Métriques détaillées")
print("4. radar_chart_top5_modeles.png - Graphique radar top 5")
print("5. analyse_ecart_performance.png - Analyse des écarts")
print("6. performance_vs_complexite.png - Performance vs complexité")
print("7. tableau_recapitulatif_performances.png - Tableau récapitulatif")

print(f"\n🏆 Classement final des modèles par R²:")
for i, (model, score) in enumerate(zip(models_sorted, r2_sorted), 1):
    print(f"{i}. {model}: {score:.4f}")
