
from sklearn.linear_model import BayesianRidge
from scipy.stats import uniform
from sklearn.model_selection import RandomizedSearchCV
from evaluationModels.evaluation_regressor import RegressionEvaluator 


class Method_BayesianRidge_Regressor:
    def __init__(self):
        self.best_parameter = None

    def train_bayesian(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'alpha_1': uniform(1e-6, 1e-3),
            'alpha_2': uniform(1e-6, 1e-3),
            'lambda_1': uniform(1e-6, 1e-3),
            'lambda_2': uniform(1e-6, 1e-3)
        }

        model = BayesianRidge()

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            cv=cv,
            scoring='neg_mean_squared_error',
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée (neg MSE) : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_bayesian_ridge_regressor(self, X_train, y_train, X_test, y_test):
        print("_Entraînement du modèle Bayesian Ridge Regression_")
        self.train_bayesian(X_train, y_train)
        y_pred = self.predict(X_test)
        print('Évaluation du modèle')
        evaluator = RegressionEvaluator(y_test, y_pred)  
        evaluator.evaluation_metrics()