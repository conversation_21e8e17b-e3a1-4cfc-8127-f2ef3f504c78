{"trial_id": "02", "hyperparameters": {"space": [{"class_name": "Int", "config": {"name": "units", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "num_layers", "default": null, "conditions": [], "min_value": 1, "max_value": 3, "step": 1, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "units_0", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "dense_units", "default": null, "conditions": [], "min_value": 64, "max_value": 256, "step": 64, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.0001, "conditions": [], "min_value": 0.0001, "max_value": 0.01, "step": null, "sampling": "log"}}, {"class_name": "Int", "config": {"name": "units_1", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "units_2", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}], "values": {"units": 128, "num_layers": 1, "units_0": 128, "dense_units": 256, "learning_rate": 0.0035766126839820184, "units_1": 96, "units_2": 32}}, "metrics": {"metrics": {}}, "score": null, "best_step": 0, "status": "RUNNING", "message": null}