from sklearn.model_selection import RandomizedSearchCV, cross_val_score
from sklearn.ensemble import RandomForestClassifier
from scipy.stats import randint as sp_randint
from evaluationModels.evaluation_classification import ClassifierEvaluator


class Method_RandomForest:
    def _init_(self):
        self.best_rf = None

    def train_rf(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        print("Veuillez patienter quelques instants...")

        rf = RandomForestClassifier()
        param_dist = {
            'n_estimators': sp_randint(50, 500),
            'max_depth': sp_randint(1, 20),
            'min_samples_split': sp_randint(2, 20),
            'min_samples_leaf': sp_randint(1, 20),
            'max_features': ['sqrt', 'log2'],
            'bootstrap': [True, False]
        }

        random_search = RandomizedSearchCV(rf, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)

        self.best_rf = random_search.best_estimator_
        print(f"Le modèle Random Forest a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")
        return self

    def predict(self, X_test):
        if self.best_rf is None:
            raise ValueError("Le modèle n'a pas été entraîné. Veuillez appeler la méthode 'train_rf' d'abord.")
        else:
            print("La prédiction avec les données de test...")

        return self.best_rf.predict(X_test)

    def run_random_forest_classifier(self, X_train, y_train, X_test, y_test):
        print("__Entraînement du modèle Random Forest__")
        # Entraînement du modèle
        self.train_rf(X_train, y_train)

        # Prédiction sur les données de test
        y_pred = self.predict(X_test)

        print('___Evaluation Metrics___')
        # Évaluation du modèle avec validation croisée pour des métriques plus réalistes
        scores = cross_val_score(self.best_rf, X_train, y_train, cv=5)
        print(f"Validation croisée (accuracy): {scores}")
        print(f"Accuracy moyenne: {scores.mean():.2f} (std: {scores.std():.2f})")

   
        # Évaluation du modèle
        evaluator = ClassifierEvaluator(y_test, y_pred) # Assurez-vous que cette classe est définie ailleurs
        evaluator.evaluation_metrics()
