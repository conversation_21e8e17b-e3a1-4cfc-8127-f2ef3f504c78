from sklearn.ensemble import <PERSON><PERSON><PERSON>BoostingRegressor
from sklearn.model_selection import RandomizedSearchCV, train_test_split
from sklearn.metrics import make_scorer, r2_score
from scipy.stats import randint, uniform
from evaluationModels.evaluation_regressor import RegressionEvaluator 

class Method_GradientBoosting_Regressor:
    def __init__(self):
        self.best_parameter = None

    def train_gbr(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        # Définir l'espace de recherche des hyperparamètres
        param_dist = {
            'n_estimators': randint(50, 500),
            'learning_rate': uniform(0.01, 0.3),
            'max_depth': randint(3, 10),
            'min_samples_split': randint(2, 20),
            'min_samples_leaf': randint(1, 10),
            'subsample': uniform(0.6, 0.4),
            'alpha': uniform(0.1, 0.9)
        }

        # Initialiser le modèle
        model = GradientBoostingRegressor(random_state=random_state)

        # Configurer la recherche aléatoire
        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(r2_score),
            cv=cv,
            random_state=random_state,
            n_jobs=-1,
            verbose=1
        )

        # Entraîner le modèle avec recherche d'hyperparamètres
        random_search.fit(X_train, y_train)

        # Stocker le meilleur modèle
        self.best_parameter = random_search.best_estimator_

        # Afficher les résultats
        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée (R²) : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def feature_importance(self, feature_names):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")

        # Obtenir l'importance des caractéristiques
        importances = self.best_parameter.feature_importances_

        # Créer un DataFrame pour l'affichage
        feature_importance_df = pd.DataFrame({
            'Feature': feature_names,
            'Importance': importances
        }).sort_values(by='Importance', ascending=False)

        # Visualiser l'importance des caractéristiques
        plt.figure(figsize=(10, 6))
        sns.barplot(x='Importance', y='Feature', data=feature_importance_df)
        plt.title('Importance des caractéristiques')
        plt.tight_layout()
        plt.show()

        return feature_importance_df

    def run_gradient_boosting_regressor(self, X_train, y_train, X_test, y_test, feature_names=None):
        print("______________Entraînement du modèle Gradient Boosting Regressor______________")
        self.train_gbr(X_train, y_train)

        y_pred = self.predict(X_test)

        print('_________________Évaluation du modèle_________________')
        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()

        # if feature_names is not None:
        #     print('_________________Importance des caractéristiques_________________')
        #     self.feature_importance(feature_names)