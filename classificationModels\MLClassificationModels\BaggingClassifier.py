from sklearn.tree import DecisionTreeClassifier
from sklearn.ensemble import BaggingClassifier
from scipy.stats import (
    randint,                   # Distribution discrète uniforme (pour entiers)
    uniform                    # Distribution continue uniforme (pour floats)
)

from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from evaluationModels.evaluation_classification import ClassifierEvaluator

class Method_Bagging_Classifier:
    def __init__(self):
        # Stocke le meilleur modèle après recherche d’hyperparamètres
        self.best_estimator_ = None

    def train(self, X, y, n_iter=20, cv=5, rs=42):
        # Initialise le Bagging avec un DecisionTree de base
        bag = BaggingClassifier(
            estimator=DecisionTreeClassifier(random_state=rs),
            random_state=rs
        )

        # Espace de recherche des hyperparamètres
        param_dist = {
            "estimator__max_depth": randint(1, 8),            # profondeur max des arbres
            "estimator__min_samples_split": randint(2, 10),   # min d’échantillons pour diviser
            "n_estimators": randint(10, 150),                 # nombre d’arbres
            "max_samples": uniform(0.5, 0.5),                 # % d’échantillons par arbre
            "max_features": uniform(0.5, 0.5),                # % de features par arbre
            "bootstrap": [True, False],                       # échantillonnage avec/sans remise
        }

        # Recherche aléatoire sur n_iter combinaisons
        search = RandomizedSearchCV(
            bag, param_dist,
            n_iter=n_iter, cv=cv,
            scoring=make_scorer(accuracy_score),
            n_jobs=-1, random_state=rs, verbose=0
        )
        search.fit(X, y)  # exécute l’optimisation
        self.best_estimator_ = search.best_estimator_  # conserve le meilleur
        print("Meilleurs hyperparamètres :", search.best_params_)
        print("Score CV moyen           :", round(search.best_score_, 4))

    def predict(self, X):
        # Prédiction avec le modèle optimisé
        return self.best_estimator_.predict(X)

    def run_bagging_classifier(self, X_train, y_train, X_test, y_test):
        self.train(X_train, y_train)
        y_pred = self.predict(X_test)
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()




