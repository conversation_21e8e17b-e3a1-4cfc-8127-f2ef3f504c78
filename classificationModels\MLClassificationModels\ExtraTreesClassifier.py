
from sklearn.ensemble import ExtraTreesClassifier
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import accuracy_score, f1_score, classification_report
from scipy.stats import randint, uniform
from evaluationModels.evaluation_classification import ClassifierEvaluator

class Method_ExtraTrees_Classifier:
    def __init__(self):
        self.best_parameter = None

    def train_extra_trees(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'n_estimators': randint(100, 1000),
            'max_depth': randint(5, 50),
            'min_samples_split': randint(2, 20),
            'min_samples_leaf': randint(1, 20),
            'max_features': ['auto', 'sqrt', 'log2', None],
            'ccp_alpha': uniform(0.0, 0.02),
            'max_leaf_nodes': randint(10, 200)
        }

        model = ExtraTreesClassifier(random_state=random_state)

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring='accuracy',
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée (accuracy) : {random_search.best_score_:.4f}")

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_extra_trees_classifier(self, X_train, y_train, X_test, y_test):
        print("__________Entraînement du modèle ExtraTrees (Classification)__________")
        self.train_extra_trees(X_train, y_train)
        y_pred = self.predict(X_test)
        print("_________________Évaluation du modèle_________________")
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
