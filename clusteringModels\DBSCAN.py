from sklearn.cluster import DBSCAN
from sklearn.metrics import silhouette_score
from sklearn.model_selection import ParameterGrid
import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
from mpl_toolkits.mplot3d import Axes3D

class DBSCANClustering:
    def __init__(self, data):
        self.data = data
        self.model = None
        self.labels = None
        self.best_params = None
        self.best_score = -1

    def _optimize_hyperparameters(self):
        print("Optimisation des hyperparamètres DBSCAN...")
        param_grid = ParameterGrid({
            'eps': [0.3, 0.5, 0.7, 1.0],
            'min_samples': [3, 5, 10],
            'metric': ['euclidean', 'manhattan', 'cosine'],
            'algorithm': ['auto', 'ball_tree', 'kd_tree'],
            'leaf_size': [20, 30, 40],
            'p': [1, 2],
            'n_jobs': [None]
        })

        for params in param_grid:
            try:
                model = DBSCAN(
                    eps=params['eps'],
                    min_samples=params['min_samples'],
                    metric=params['metric'],
                    algorithm=params['algorithm'],
                    leaf_size=params['leaf_size'],
                    p=params['p'],
                    n_jobs=params['n_jobs']
                )
                labels = model.fit_predict(self.data)
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)
                if n_clusters <= 1 or len(set(labels)) == len(self.data):
                    continue
                score = silhouette_score(self.data, labels)
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    self.model = model
                    self.labels = labels
            except Exception as e:
                continue

        if self.best_params:
            print(f"✔ Meilleurs paramètres : {self.best_params}")
            print(f"✔ Meilleur Silhouette Score : {self.best_score:.4f}")
        else:
            print("Aucun clustering valable trouvé.")

    def _fit_model(self):
        if self.model is None:
            print("Aucun modèle DBSCAN valide trouvé.")
        else:
            self.labels = self.model.fit_predict(self.data)

    def _describe_clusters(self):
        if self.labels is None:
            print("Aucun cluster à décrire.")
            return
        unique_labels = set(self.labels)
        print("\nDescription des clusters :")
        for label in sorted(unique_labels):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            label_name = "Bruit" if label == -1 else f"Cluster {label}"
            print(f"{label_name} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        dims = self.data.shape[1]
        data = self.data.values if isinstance(self.data, pd.DataFrame) else self.data
        labels = self.labels

        if dims == 2:
            plt.scatter(data[:, 0], data[:, 1], c=labels, cmap='tab10', s=40)
            plt.title("Clustering DBSCAN (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.grid(True)
            plt.show()

        elif dims == 3:
            fig = plt.figure()
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(data[:, 0], data[:, 1], data[:, 2], c=labels, cmap='tab10', s=40)
            ax.set_title("Clustering DBSCAN (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.show()
        else:
            print("Visualisation uniquement disponible pour 2D ou 3D.")

    def run_dbscan_clustering(self):
        self._optimize_hyperparameters()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
