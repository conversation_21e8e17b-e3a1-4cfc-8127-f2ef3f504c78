from classificationModels.RNNClassificationModels.GRUClassifier import Method_GRU_Classifier 
from classificationModels.RNNClassificationModels.LSTMClassifier import Method_LSTM_Classifier


class ClassificationRNNModels_main:
    def __init__(self):
        model_list = [
            ('LSTM', Method_LSTM_Classifier()), 
            ('GRU', Method_GRU_Classifier())
        ]
        self.models = {i+1: model for i, model in enumerate(model_list)}

    def select_models(self): #cette fonction a été modifier pae l'équipe test
        print("\n" + "="*80)
        print("Construction et évaluation des modèles de deep learning".center(80))
        print("="*80)
        print("Puisque vous avez un problème de classification")
        print("Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparés par des virgules (e.g., 1, 3, 5):")

        while True:
            # Display available models
            for idx, (name, _) in self.models.items():
                print(f"{idx}. {name}")

            # Get user input
            selected_indices = input("Votre choix : ").split(',')

            # Validate input
            selected_indices = [
                int(idx.strip()) for idx in selected_indices 
                if idx.strip().isdigit() and int(idx.strip()) in self.models
            ]

            if selected_indices:
                print(f"Vous avez sélectionné les modèles : {', '.join(map(str, selected_indices))}.")
                return selected_indices
            else:
                print("Erreur : Votre choix contient des valeurs invalides. Merci de réessayer.")

    def models_selected_classifier(self, model_indices, X_train, y_train, X_test, y_test):
        for idx in model_indices:
            model_name, model = self.models[idx]

            # Construction dynamique du nom de la méthode
            # Exemple : 'KNN' → 'run_knn_classifier'
            method_name = f"run_{model_name.lower().replace(' ', '_')}_classifier"

            if hasattr(model, method_name):
                print("\n" + "-"*80)
                print(f"MÉTHODE : {model_name}".center(80))
                print("-"*80)
                getattr(model, method_name)(X_train, y_train, X_test, y_test)
                print("\n" + "-"*80)
                print(f"Processus '{model_name}' terminé avec succès.".center(80))
                print("-"*80 + "\n")
                print("="*80 + "\n")
            else:
                print(f"Méthode '{method_name}' introuvable pour le modèle {model_name}")

    def run_models_selected_classifier(self, X_train, y_train, X_test, y_test):
        selected_indices = self.select_models()
        print("qdsjfhbskjhfkjf : ",selected_indices)
        self.models_selected_classifier(selected_indices, X_train, y_train, X_test, y_test)