<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Analyse et Prédiction des Besoins en Ressources Humaines pour un Café</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
        }
        h1, h2, h3, h4 {
            color: #2c3e50;
        }
        h1 {
            text-align: center;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
        }
        h2 {
            border-bottom: 1px solid #3498db;
            padding-bottom: 5px;
        }
        .section {
            margin-bottom: 30px;
        }
        .code {
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
            padding: 15px;
            font-family: Consolas, monospace;
            overflow-x: auto;
        }
        table {
            border-collapse: collapse;
            width: 100%;
            margin: 20px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
        .highlight {
            background-color: #ffffcc;
            padding: 2px;
        }
        .figure {
            text-align: center;
            margin: 20px 0;
        }
        .figure img {
            max-width: 100%;
            height: auto;
            border: 1px solid #ddd;
        }
        .figure-caption {
            font-style: italic;
            margin-top: 10px;
        }
        .note {
            background-color: #e7f3fe;
            border-left: 6px solid #2196F3;
            padding: 10px;
            margin: 15px 0;
        }
        .warning {
            background-color: #fff3cd;
            border-left: 6px solid #ffc107;
            padding: 10px;
            margin: 15px 0;
        }
        .success {
            background-color: #d4edda;
            border-left: 6px solid #28a745;
            padding: 10px;
            margin: 15px 0;
        }
    </style>
</head>
<body>
    <h1>Analyse et Prédiction des Besoins en Ressources Humaines pour un Café</h1>

    <div class="section" id="introduction">
        <h2>IV.1 Introduction</h2>
        <p>
            Dans le cadre de notre projet d'analyse des ressources humaines dans le secteur de la restauration, avec un accent particulier sur les cafés et établissements similaires, ce chapitre constitue une étape cruciale dans notre démarche analytique. Ayant identifié les données pertinentes et collecté les informations nécessaires sur les employés, leurs compétences, et les indicateurs de performance, notre objectif principal est désormais de développer des modèles d'apprentissage capables de prédire avec précision les besoins en ressources humaines et d'optimiser la satisfaction client.
        </p>
        <p>
            Ce chapitre se concentre sur la mise en œuvre de deux approches complémentaires d'apprentissage automatique : les méthodes supervisées et les méthodes de clustering. Ces approches sont choisies en raison de leur efficacité éprouvée dans la modélisation de données complexes et leur capacité à traiter des ensembles de données de grande taille. Les méthodes supervisées, telles que la régression et les arbres de décision, sont particulièrement adaptées à notre problématique, car elles permettent de prédire des variables cibles comme la performance de satisfaction client ou les coûts opérationnels en fonction des caractéristiques des employés. D'autre part, les méthodes de clustering, comme K-means et la classification hiérarchique, nous permettront d'identifier des groupes distincts d'employés partageant des caractéristiques similaires, ce qui pourrait révéler des schémas cachés et faciliter la gestion des ressources humaines.
        </p>
        <p>
            Ce chapitre est organisé de la manière suivante : nous commencerons par une présentation détaillée des données utilisées pour l'entraînement et la validation de nos modèles, incluant les variables relatives aux postes, aux compétences, à l'expérience et aux indicateurs de performance. Ensuite, nous expliquerons en détail le fonctionnement des différentes méthodes supervisées et de clustering, ainsi que leur adaptation à notre contexte spécifique de gestion des ressources humaines dans un café. Nous discuterons également des différentes métriques et techniques d'évaluation que nous utiliserons pour mesurer la performance de nos modèles. Enfin, nous présenterons les résultats obtenus à travers des analyses approfondies et des visualisations significatives, permettant ainsi de tirer des conclusions pertinentes quant à l'efficacité de nos approches et de formuler des recommandations concrètes pour optimiser la gestion des ressources humaines et améliorer la satisfaction client.
        </p>
        <p>
            L'intégration de ces modèles d'apprentissage dans le processus décisionnel des établissements de restauration représente une avancée significative dans la modernisation de la gestion des ressources humaines, permettant une allocation plus efficace du personnel, une meilleure planification des horaires, et ultimement, une amélioration de l'expérience client et de la rentabilité.
        </p>
    </div>

    <div class="section" id="analyse-donnees">
        <h2>IV.2 Analyse des Données</h2>

        <div class="subsection">
            <h3>IV.2.1 Aperçu des Données</h3>
            <p>
                Notre jeu de données contient des informations sur 10 000 enregistrements liés aux employés d'un café,
                avec 18 variables différentes. Ces variables comprennent des informations sur les postes, les tâches,
                les compétences, l'expérience, et divers indicateurs de performance.
            </p>

            <div class="code">
                <pre>
▶ Cinq premières et dernières lignes du jeu de données:
               poste           tache_principale  ... satisfaction_client satisfaction_client_performance
0           caissier               encaissement  ...               eleve                             83
1            serveur              Service-table  ...               eleve                             80
2           caissier               encaissement  ...               eleve                             90
3          nettoyeur          Nettoyage-cuisine  ...               moyen                             75
4           caissier           Service-comptoir  ...               eleve                             85
...              ...                        ...  ...                 ...                            ...
9995         serveur               Encaissement  ...               eleve                             83
9996         serveur           Nettoyage-tables  ...               eleve                             94
9997        caissier             Gestion-caisse  ...               eleve                             82
9998         barista  Gestion-stock-ingredients  ...               eleve                             87
9999  receptionniste            Accueil-clients  ...               eleve                             88

[10000 rows x 18 columns]
                </pre>
            </div>
            <p class="figure-caption">Figure 1: Aperçu des premières et dernières lignes du jeu de données des employés du café</p>

            <div class="figure">
                <img src="images/apercu_donnees.png" alt="Aperçu des données">
                <div class="figure-caption">Figure 2: Représentation visuelle du jeu de données des employés du café</div>
            </div>

            <p>
                Le jeu de données comprend 10 000 lignes et 18 colonnes, offrant une base solide pour notre analyse.
            </p>

            <div class="code">
                <pre>
▶ Nombre total de lignes et de colonnes:
(10000, 18)
                </pre>
            </div>
            <p class="figure-caption">Figure 3: Dimensions du jeu de données (nombre de lignes et de colonnes)</p>

            <p>
                Voici les noms des colonnes disponibles dans notre jeu de données:
            </p>

            <div class="code">
                <pre>
▶ Noms des colonnes:
['poste', 'tache_principale', 'competence_requise', 'niveau_experience',
'heures_travail_jour', 'clients_par_heure', 'temps_par_client_minutes',
'charge_travail_pic', 'productivite_moyenne', 'cout_horaire',
'formation_requise_jours', 'disponibilite_horaire', 'stress_poste',
'complexite_taches', 'duree_moyenne_minutes', 'priorite',
'satisfaction_client', 'satisfaction_client_performance']
                </pre>
            </div>
            <p class="figure-caption">Figure 4: Liste des variables disponibles dans le jeu de données</p>
        </div>

        <div class="subsection">
            <h3>IV.2.2 Types de Données</h3>
            <p>
                Il est important de comprendre les types de données pour chaque variable afin d'appliquer
                les traitements appropriés. Voici les types de données pour chaque colonne:
            </p>

            <div class="code">
                <pre>
poste                               object
tache_principale                    object
competence_requise                  object
niveau_experience                   object
heures_travail_jour                float64
clients_par_heure                    int64
temps_par_client_minutes           float64
charge_travail_pic                  object
productivite_moyenne                 int64
cout_horaire                       float64
formation_requise_jours              int64
disponibilite_horaire               object
stress_poste                        object
complexite_taches                   object
duree_moyenne_minutes              float64
priorite                            object
satisfaction_client                 object
satisfaction_client_performance      int64
dtype: object
                </pre>
            </div>
            <p class="figure-caption">Figure 5: Types de données pour chaque variable du jeu de données</p>

            <p>
                Nous avons décidé de conserver les types de données actuels car ils sont appropriés pour notre analyse:
                <ul>
                    <li>Les variables catégorielles (comme 'poste', 'tache_principale') sont de type 'object'</li>
                    <li>Les variables numériques continues (comme 'heures_travail_jour', 'cout_horaire') sont de type 'float64'</li>
                    <li>Les variables numériques discrètes (comme 'clients_par_heure', 'productivite_moyenne') sont de type 'int64'</li>
                </ul>
            </p>
            <p class="note">
                <strong>Note:</strong> Nous avons choisi de ne pas modifier les types de données (option 2: Non) car les types actuels
                sont appropriés pour notre analyse. Les transformations nécessaires (comme l'encodage des variables catégorielles)
                seront effectuées automatiquement lors des étapes ultérieures du prétraitement.
            </p>
        </div>

        <div class="subsection">
            <h3>IV.2.3 Statistiques Descriptives</h3>
            <p>
                Pour mieux comprendre nos données, examinons les statistiques descriptives pour les variables
                quantitatives et qualitatives.
            </p>

            <h4>Statistiques pour les variables quantitatives:</h4>
            <div class="code">
                <pre>
▶ Statistique descriptive pour les variables quantitatives
       heures_travail_jour  ...  satisfaction_client_performance
count         10000.000000  ...                     10000.000000
mean              7.587190  ...                        83.341800
std               1.115917  ...                         7.028464
min               5.000000  ...                        70.000000
25%               6.600000  ...                        78.000000
50%               7.800000  ...                        85.000000
75%               8.400000  ...                        89.000000
max               9.700000  ...                        95.000000
[8 rows x 8 columns]
                </pre>
            </div>
            <p class="figure-caption">Figure 6: Statistiques descriptives des variables quantitatives</p>

            <p>
                Ces statistiques nous révèlent plusieurs informations importantes:
                <ul>
                    <li>Les employés travaillent en moyenne 7,59 heures par jour, avec un minimum de 5 heures et un maximum de 9,7 heures</li>
                    <li>La performance de satisfaction client varie de 70 à 95, avec une moyenne de 83,34</li>
                    <li>50% des employés ont une performance de satisfaction client supérieure à 85 (médiane)</li>
                </ul>
            </p>

            <h4>Statistiques pour les variables qualitatives:</h4>
            <div class="code">
                <pre>
▶ Statistique descriptive pour les variables qualitatives
          poste tache_principale competence_requise  ... complexite_taches priorite satisfaction_client
unique        7               23                 24  ...                 3        3                   2
top     serveur    Nettoyage-sol          attention  ...             moyen  moyenne               eleve
freq       1469              510                     ...                                            83
                </pre>
            </div>
            <p class="figure-caption">Figure 7: Statistiques descriptives des variables qualitatives</p>

            <p>
                Ces statistiques nous montrent:
                <ul>
                    <li>Il y a 7 postes différents, avec 'serveur' comme poste le plus fréquent (1469 occurrences)</li>
                    <li>Il y a 23 tâches principales différentes, avec 'Nettoyage-sol' comme la plus fréquente (510 occurrences)</li>
                    <li>La compétence requise la plus fréquente est 'attention'</li>
                    <li>La satisfaction client est majoritairement 'eleve'</li>
                </ul>
            </p>
        </div>
    </div>

    <div class="section" id="visualisation">
        <h2>IV.3 Visualisation des Données</h2>

        <div class="subsection">
            <h3>IV.3.1 Choix des Visualisations</h3>
            <p>
                La visualisation des données est une étape cruciale pour comprendre les distributions et les relations
                entre les variables. Nous avons choisi d'utiliser plusieurs types de visualisations pour explorer nos données.
            </p>

            <h4>Visualisation des variables continues:</h4>
            <p>
                Pour les variables continues, nous avons choisi d'utiliser des boxplots (boîtes à moustaches) pour visualiser
                leur distribution. Les boxplots nous permettent de voir la médiane, les quartiles et les valeurs aberrantes.
            </p>
            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné l'option "1. Oui" pour visualiser les boxplots des variables continues.
                Cette visualisation est essentielle pour identifier les distributions et les valeurs aberrantes dans nos données numériques.
            </p>

            <div class="figure">
                <img src="images/boxplots_variables_continues.png" alt="Boxplots des variables continues">
                <div class="figure-caption">Figure 8: Boxplots des variables numériques continues</div>
            </div>

            <p>
                Les boxplots révèlent plusieurs informations importantes:
                <ul>
                    <li>La variable 'satisfaction_client_performance' présente une distribution légèrement asymétrique vers la gauche</li>
                    <li>'heures_travail_jour' montre une distribution relativement uniforme entre 6 et 9 heures</li>
                    <li>'cout_horaire' présente quelques valeurs aberrantes élevées</li>
                    <li>'productivite_moyenne' montre une distribution assez symétrique</li>
                </ul>
            </p>

            <h4>Visualisation des relations entre variables continues:</h4>
            <p>
                Pour explorer les relations entre les variables continues, nous avons utilisé un pairplot qui affiche
                les nuages de points pour chaque paire de variables.
            </p>
            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné l'option "1. Oui" pour visualiser les variables continues avec un pairplot.
                Cette visualisation nous permet d'identifier les corrélations et les tendances entre les variables numériques.
            </p>

            <div class="figure">
                <img src="images/pairplot_variables_continues.png" alt="Pairplot des variables continues">
                <div class="figure-caption">Figure 9: Pairplot montrant les relations entre les variables numériques</div>
            </div>

            <p>
                Le pairplot révèle plusieurs relations intéressantes:
                <ul>
                    <li>Une corrélation positive entre 'productivite_moyenne' et 'satisfaction_client_performance'</li>
                    <li>Une corrélation positive entre 'niveau_experience' et 'satisfaction_client_performance'</li>
                    <li>Une corrélation négative entre 'temps_par_client_minutes' et 'clients_par_heure'</li>
                </ul>
            </p>

            <h4>Visualisation des variables catégorielles:</h4>
            <p>
                Pour les variables catégorielles, nous avons choisi d'utiliser des diagrammes en camembert pour visualiser
                la distribution des différentes catégories.
            </p>
            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné l'option "1. Oui" pour visualiser les variables catégorielles avec des diagrammes en camembert.
                Cette visualisation nous permet de comprendre la répartition des différentes catégories dans nos données.
            </p>

            <div class="figure">
                <img src="images/pie_charts_variables_categorielles.png" alt="Diagrammes en camembert des variables catégorielles">
                <div class="figure-caption">Figure 10: Diagrammes en camembert montrant la distribution des variables catégorielles</div>
            </div>

            <p>
                Les diagrammes en camembert révèlent:
                <ul>
                    <li>La répartition des postes: les serveurs représentent la plus grande proportion, suivis des caissiers</li>
                    <li>La distribution des niveaux d'expérience: la majorité des employés ont un niveau d'expérience 'moyen' ou 'élevé'</li>
                    <li>La satisfaction client est majoritairement 'élevée'</li>
                </ul>
            </p>
        </div>
    </div>

    <div class="section" id="pretraitement">
        <h2>IV.4 Prétraitement des Données</h2>

        <div class="subsection">
            <h3>IV.4.1 Vérification de la Qualité des Données</h3>
            <p>
                Avant de procéder à la modélisation, nous avons effectué plusieurs vérifications pour assurer la qualité des données.
            </p>

            <h4>Vérification des doublons:</h4>
            <div class="code">
                <pre>
▶ Vérification des doublons...
Il n'y a pas de lignes dupliquées.
                </pre>
            </div>
            <p class="figure-caption">Figure 11: Résultat de la vérification des doublons dans le jeu de données</p>
            <p>
                Aucune ligne dupliquée n'a été détectée dans notre jeu de données, ce qui est positif pour la qualité de nos données.
            </p>

            <h4>Vérification des valeurs manquantes:</h4>
            <div class="code">
                <pre>
▶ Vérification des valeurs manquantes...
Pourcentage de valeurs manquantes par colonne:
poste: 0.00%
tacheprincipale: 0.00%
competencerequise: 0.00%
niveauexperience: 0.00%
heurestravailjour: 0.00%
clientsparheure: 0.00%
tempsparclientminutes: 0.00%
chargetravailpic: 0.00%
productivitemoyenne: 0.00%
couthoraire: 0.00%
formationrequisejours: 0.00%
disponibilitehoraire: 0.00%
stressposte: 0.00%
dureemoyenneminutes: 0.00%
priorite: 0.00%
satisfactionclient: 0.00%
satisfactionclientperformance: 0.00%
                </pre>
            </div>
            <p class="figure-caption">Figure 12: Pourcentage de valeurs manquantes par colonne</p>
            <p>
                Aucune valeur manquante n'a été détectée dans notre jeu de données, ce qui est excellent pour la qualité de nos analyses.
                Nous n'avons donc pas besoin d'appliquer des techniques d'imputation.
            </p>
            <p class="success">
                <strong>Point fort:</strong> Notre jeu de données est complet, sans valeurs manquantes ni doublons, ce qui renforce
                la fiabilité de nos analyses et de nos modèles prédictifs.
            </p>
        </div>

        <div class="subsection">
            <h3>IV.4.2 Choix du Type de Problème</h3>
            <p>
                Pour notre analyse des besoins en ressources humaines, nous devons d'abord déterminer le type de problème
                que nous souhaitons résoudre: classification/régression ou clustering.
            </p>

            <div class="code">
                <pre>
▶ Choisissez le type de problème :
1. Classification/Régression
2. Clustering
Entrez le numéro correspondant à votre choix (1-2) : 1
                </pre>
            </div>
            <p class="figure-caption">Figure 13: Choix du type de problème d'apprentissage automatique</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons choisi l'option "1. Classification/Régression" car notre objectif est de
                prédire une variable cible spécifique à partir des caractéristiques des employés. Cette approche supervisée
                nous permettra de développer un modèle prédictif pour anticiper les besoins en ressources humaines.
            </p>

            <p>
                Après avoir choisi le type de problème, nous devons sélectionner la variable cible (dépendante) que nous
                souhaitons prédire:
            </p>

            <div class="code">
                <pre>
Merci de sélectionner le numéro correspondant à la variable de sortie à partir de cette liste :
0. poste
1. tacheprincipale
3. niveauexperience
4. heurestravailjour
5. clientsparheure
6. tempsparclientminutes
7. chargetravailpic
8. productivitemoyenne
9. couthoraire
10. formationrequisejours
11. disponibilitehoraire
12. stressposte
13. complexitetaches
14. dureemoyenneminutes
15. priorite
16. satisfactionclient
17. satisfactionclientperformance
Entrez le numéro de la variable de sortie : 17
La variable de sortie sélectionnée est 'satisfactionclientperformance'.
'satisfactionclientperformance' est une variable quantitative. Nous avons un problème de régression.
                </pre>
            </div>
            <p class="figure-caption">Figure 14: Sélection de la variable cible pour le problème de régression</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné 'satisfaction_client_performance' comme variable cible
                car elle représente un indicateur clé de performance pour les ressources humaines dans un café. Prédire cette
                variable nous permettra d'identifier les facteurs qui influencent la satisfaction des clients et d'optimiser
                l'allocation des ressources humaines en conséquence.
            </p>

            <p>
                Comme 'satisfaction_client_performance' est une variable numérique continue (score de 70 à 95), notre problème
                est identifié comme un problème de régression. Nous allons donc développer des modèles de régression pour
                prédire ce score de performance.
            </p>
        </div>

        <div class="subsection">
            <h3>IV.4.3 Sélection des Variables Importantes</h3>
            <p>
                Pour améliorer la performance de nos modèles, nous avons appliqué une méthode statistique pour sélectionner
                les variables les plus importantes de notre jeu de données.
            </p>

            <div class="code">
                <pre>
▶ Souhaitez-vous appliquer une méthode statistique pour sélectionner les variables les plus importantes de votre dataset?
1. Oui
2. Non
Votre choix (1-2) : 1
______________Sélection des variables les plus importantes...
 ______________
Types détectés : X -> {'poste': 'catégorielle', 'tacheprincipale': 'catégorielle', 'competencerequise': 'catégorielle',
'niveauexperience': 'catégorielle', 'heurestravailjour': 'numérique', 'clientsparheure': 'numérique',
'tempsparclientminutes': 'numérique', 'chargetravailpic': 'catégorielle', 'productivitemoyenne': 'numérique',
'couthoraire': 'numérique', 'formationrequisejours': 'numérique', 'disponibilitehoraire': 'catégorielle',
'stressposte': 'catégorielle', 'complexitetaches': 'catégorielle'}, y -> numérique
Dimensions de X : (10000, 17), Dimensions de y : (10000,)
Variables mixtes, application automatique de l'Information Mutuelle.

 **Scores d'Information Mutuelle (triés) :**
1. niveauexperience: 0.8067
2. heurestravailjour: 0.7077
3. productivitemoyenne: 0.6431
4. couthoraire: 0.6268
5. satisfactionclient: 0.5883
6. formationrequisejours: 0.3113
7. clientsparheure: 0.2374
8. tempsparclientminutes: 0.1615
9. disponibilitehoraire: 0.0068
10. tacheprincipale: 0.0044
11. stressposte: 0.0038
12. competencerequise: 0.0016
13. dureemoyenneminutes: 0.0012
14. poste: 0.0000
15. chargetravailpic: 0.0000
16. complexitetaches: 0.0000
17. priorite: 0.0000
                </pre>
            </div>
            <p class="figure-caption">Figure 15: Scores d'Information Mutuelle pour la sélection des variables importantes</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons choisi d'appliquer l'Information Mutuelle comme méthode de sélection
                des variables car elle est adaptée aux données mixtes (catégorielles et numériques) et mesure la dépendance
                entre chaque variable et la cible sans faire d'hypothèses sur la forme de cette relation.
            </p>

            <p>
                Les résultats de l'Information Mutuelle nous révèlent que:
                <ul>
                    <li>Les variables les plus importantes pour prédire 'satisfaction_client_performance' sont:
                        <ol>
                            <li>'niveau_experience' (0.8067) - Le niveau d'expérience des employés a l'impact le plus fort</li>
                            <li>'heures_travail_jour' (0.7077) - Le nombre d'heures travaillées par jour</li>
                            <li>'productivite_moyenne' (0.6431) - La productivité moyenne des employés</li>
                            <li>'cout_horaire' (0.6268) - Le coût horaire des employés</li>
                            <li>'satisfaction_client' (0.5883) - La satisfaction client qualitative</li>
                        </ol>
                    </li>
                    <li>Plusieurs variables ont une importance très faible (score proche de 0), comme 'poste', 'complexite_taches' et 'priorite'</li>
                </ul>
            </p>

            <div class="code">
                <pre>
 Combien de features souhaitez-vous garder parmi 17 ? (Entrez un nombre) : 15
niveauexperience       Information Mutuelle  0.806675
heurestravailjour      Information Mutuelle  0.707658
productivitemoyenne    Information Mutuelle  0.643065
couthoraire            Information Mutuelle    0.6268
satisfactionclient     Information Mutuelle  0.588343
formationrequisejours  Information Mutuelle  0.311276
clientsparheure        Information Mutuelle  0.237434
tempsparclientminutes  Information Mutuelle  0.161496
tacheprincipale        Information Mutuelle  0.004358
stressposte            Information Mutuelle  0.003754
competencerequise      Information Mutuelle  0.001598
dureemoyenneminutes    Information Mutuelle  0.001222
chargetravailpic       Information Mutuelle       0.0
Les variables caractéristiques sélectionnées : ['niveauexperience', 'heurestravailjour', 'productivitemoyenne',
'couthoraire', 'satisfactionclient', 'formationrequisejours', 'clientsparheure', 'tempsparclientminutes',
'disponibilitehoraire', 'tacheprincipale', 'stressposte', 'competencerequise', 'dureemoyenneminutes',
'poste', 'chargetravailpic']
Les variables caractéristiques supprimées : ['complexitetaches', 'priorite']
1. Garder les variables sélectionnées puis continuer.
2. Essayer une autre méthode de feature selection.
3. Ignorer l'étape de feature selection puis continuer.
Veuillez choisir une option (1-3) : 1
Dimensions finales de X : (10000, 15)
Dimensions finales de y : (10000,)
La sélection des variables statistiquement les plus importantes de votre dataset a été effectuée avec succès.
                </pre>
            </div>
            <p class="figure-caption">Figure 16: Sélection finale des variables à conserver pour la modélisation</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons décidé de conserver 15 variables sur 17, en supprimant uniquement
                'complexitetaches' et 'priorite' qui avaient les scores d'importance les plus faibles (0.0000). Cette approche
                nous permet de garder la plupart des informations tout en éliminant les variables non pertinentes.
            </p>

            <div class="figure">
                <img src="images/importance_variables.png" alt="Importance des variables">
                <div class="figure-caption">Figure 17: Importance des variables selon l'Information Mutuelle</div>
            </div>
        </div>

        <div class="subsection">
            <h3>IV.4.4 Normalisation et Encodage des Données</h3>
            <p>
                Pour préparer nos données à la modélisation, nous avons appliqué des techniques de normalisation pour les
                variables numériques et d'encodage pour les variables catégorielles.
            </p>

            <h4>Normalisation des données numériques:</h4>
            <div class="code">
                <pre>
▶  Souhaitez-vous appliquer la normalisation des données ?
1. Oui
2. Non
Veuillez entrer votre choix (1-2) : 1

Options de normalisation des caractéristiques :
1. Normalisation Min-Max (redimensionne les données entre 0 et 1)
2. Standardisation Z-score (moyenne = 0, écart-type = 1)
Entrez votre choix (1 ou 2) : 2
✔ Standardisation Z-score appliquée avec succès.
                </pre>
            </div>
            <p class="figure-caption">Figure 18: Choix et application de la méthode de normalisation des données</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons choisi d'appliquer la normalisation (option 1: Oui) car elle est
                essentielle pour les algorithmes de régression. Parmi les méthodes de normalisation, nous avons opté pour la
                Standardisation Z-score (option 2) car elle est moins sensible aux valeurs aberrantes que la normalisation Min-Max
                et fonctionne mieux avec les algorithmes qui supposent une distribution normale des données.
            </p>

            <p>
                La standardisation Z-score transforme les données pour qu'elles aient une moyenne de 0 et un écart-type de 1,
                ce qui permet de comparer des variables avec des échelles différentes et améliore la convergence des algorithmes
                d'apprentissage.
            </p>

            <h4>Encodage des variables catégorielles:</h4>
            <div class="code">
                <pre>
Options d'encodage pour toutes les colonnes catégorielles :
1. Encodage Label (entier)
2. Encodage One-Hot
3. Target Encoding
4. Aucune transformation
Veuillez entrer votre choix (1-4) : 3
Veuillez entrer le nom de la colonne cible pour le Target Encoding : satisfactionclientperformance
Target Encoding appliqué à toutes les colonnes catégorielles en utilisant la colonne cible 'satisfactionclientperformance'.
                </pre>
            </div>
            <p class="figure-caption">Figure 19: Choix et application de la méthode d'encodage des variables catégorielles</p>

            <p class="note">
                <strong>Choix effectué:</strong> Pour les variables catégorielles, nous avons choisi le Target Encoding (option 3)
                car cette méthode est particulièrement adaptée aux problèmes de régression. Elle remplace chaque catégorie par
                la moyenne de la variable cible pour cette catégorie, ce qui capture efficacement la relation entre les variables
                catégorielles et la variable cible.
            </p>

            <p>
                Le Target Encoding présente plusieurs avantages pour notre cas:
                <ul>
                    <li>Il gère bien les variables catégorielles avec de nombreuses catégories (comme 'tache_principale' avec 23 catégories)</li>
                    <li>Il capture la relation entre chaque catégorie et la variable cible</li>
                    <li>Il évite l'explosion dimensionnelle que pourrait causer l'encodage One-Hot</li>
                    <li>Il est particulièrement efficace pour les problèmes de régression</li>
                </ul>
            </p>
        </div>

        <div class="subsection">
            <h3>IV.4.5 Préparation Finale des Données</h3>

            <h4>Vérification de la nature chronologique:</h4>
            <div class="code">
                <pre>
▶ Le problème à résoudre est-il de nature chronologique ?
1. Oui
2. Non
Veuillez entrer votre choix (1-2) : 2
Le problème n'est pas de nature chronologique. Méthode ignorée.
                </pre>
            </div>
            <p class="figure-caption">Figure 20: Vérification de la nature chronologique du problème</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons indiqué que notre problème n'est pas de nature chronologique (option 2: Non)
                car nos données ne représentent pas une série temporelle. Il s'agit plutôt d'observations indépendantes d'employés
                à un moment donné, sans ordre temporel spécifique.
            </p>

            <h4>Conservation des variables:</h4>
            <div class="code">
                <pre>
Voulez-vous conserver toutes les variables ?
 1. Oui
 2. Non
Veuillez entrer votre choix (1-2) :1
Toutes les variables ont été conservées.
                </pre>
            </div>
            <p class="figure-caption">Figure 21: Choix de conservation des variables sélectionnées</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons choisi de conserver toutes les variables sélectionnées précédemment (option 1: Oui)
                car nous avons déjà effectué une sélection de variables basée sur l'Information Mutuelle, et toutes les variables
                restantes peuvent potentiellement contribuer à la prédiction de notre variable cible.
            </p>

            <h4>Division des données:</h4>
            <div class="code">
                <pre>
▶ Veuillez entrer la taille de l'ensemble de test (0.0 < taille < 1.0) : 0.2
Les données ont été divisées en ensembles d'entraînement et de test avec :
        X_train shape: (8000, 17)
        X_test shape: (2000, 17)
        y_train shape: (8000,)
        y_test shape: (2000,)
                </pre>
            </div>
            <p class="figure-caption">Figure 22: Division des données en ensembles d'entraînement et de test</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons choisi une taille d'ensemble de test de 0.2 (20% des données) car
                c'est une proportion standard qui offre un bon équilibre entre:
                <ul>
                    <li>Avoir suffisamment de données pour l'entraînement (80% = 8000 observations)</li>
                    <li>Avoir un ensemble de test représentatif pour évaluer la performance (20% = 2000 observations)</li>
                </ul>
            </p>

            <p class="success">
                <strong>Prétraitement terminé:</strong> Nos données sont maintenant prêtes pour la modélisation. Nous avons:
                <ul>
                    <li>Sélectionné les variables les plus importantes</li>
                    <li>Normalisé les variables numériques avec la standardisation Z-score</li>
                    <li>Encodé les variables catégorielles avec le Target Encoding</li>
                    <li>Divisé les données en ensembles d'entraînement (80%) et de test (20%)</li>
                </ul>
            </p>
        </div>
    </div>

    <div class="section" id="modelisation">
        <h2>IV.5 Construction et Évaluation des Modèles de Machine Learning (Méthodes Supervisées)</h2>

        <div class="subsection">
            <h3>IV.5.1 Sélection des Modèles de Régression</h3>
            <p>
                Après avoir préparé nos données, nous avons sélectionné plusieurs modèles de régression pour prédire
                la performance de satisfaction client.
            </p>

            <div class="code">
                <pre>
Puisque vous avez un problème de régression
Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparés par des virgules (e.g., 1, 3):
1. KNN
2. Decision Tree
3. Random Forest
4. SVR
5. Linear regression
6. Bayesian linear
7. Bayesian Ridge
8. Gaussian Process regression
9. Gradient Boosting
10. AdaBoost
11. XGBoost
12. Catboost
13. Extra trees
14. LightGBM
Votre choix : 1,2,3,5,8,9,11,13
                </pre>
            </div>
            <p class="figure-caption">Figure 23: Sélection des modèles de régression à évaluer</p>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné une variété de modèles de régression pour comparer
                leurs performances et identifier le plus adapté à notre problème. Notre sélection comprend:
                <ul>
                    <li>Des modèles simples: KNN, Decision Tree, Linear regression</li>
                    <li>Des modèles d'ensemble: Random Forest, Gradient Boosting, XGBoost, Extra trees</li>
                    <li>Des modèles probabilistes: Gaussian Process regression</li>
                </ul>
                Cette diversité nous permettra de comparer différentes approches et de choisir le modèle offrant le meilleur
                équilibre entre précision et interprétabilité.
            </p>
        </div>

        <div class="subsection">
            <h3>IV.5.2 Évaluation des Modèles</h3>
            <p>
                Nous avons entraîné et évalué chaque modèle sélectionné. Voici les résultats pour les modèles les plus performants:
            </p>

            <h4>IV.5.2.1 K-Nearest Neighbors (KNN)</h4>
            <div class="code">
                <pre>
================================================================================
                                 MÉTHODE : KNN
================================================================================
_________________Entraînement du modèle KNN pour la régression_________________
Veuillez patienter quelques instants...
Le modèle KNN de régression a été entraîné avec les meilleurs hyperparamètres: {'metric': 'manhattan', 'n_neighbors': 24}.
La prédiction avec les données de test...
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2205
Racine de l'erreur quadratique (RMSE) : 0.4696
Erreur absolue moyenne (MAE)          : 0.4059
Coefficient de détermination (R²)     : 0.7831
Score de variance expliquée           : 0.7833
                </pre>
            </div>
            <p class="figure-caption">Figure 31: Résultats de l'entraînement et de l'évaluation du modèle KNN</p>

            <p class="note">
                <strong>Description de la Figure 32:</strong> La visualisation des prédictions du modèle KNN (non disponible actuellement) montrerait un nuage de points comparant les valeurs prédites (axe Y) et les valeurs réelles (axe X) de la satisfaction client. Les points seraient distribués autour de la ligne diagonale (prédiction parfaite), avec une dispersion modérée reflétant l'erreur quadratique moyenne (RMSE) de 0.4696. On observerait probablement une tendance à sous-estimer légèrement les valeurs extrêmes, ce qui est typique des modèles KNN qui prédisent des moyennes locales.
            </p>

            <p>
                Le modèle KNN a obtenu un coefficient de détermination (R²) de 0.7831, ce qui signifie qu'il explique
                environ 78.3% de la variance dans les données de test. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Métrique de distance: manhattan</li>
                    <li>Nombre de voisins: 24</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle KNN:</strong> Le modèle KNN (K-Nearest Neighbors) fonctionne en identifiant les 24 points de données les plus proches d'une nouvelle observation et en prédisant la valeur moyenne de ces voisins. Avec un R² de 0.7831, ce modèle offre une bonne performance prédictive. L'utilisation de la distance de Manhattan (somme des différences absolues) plutôt que la distance euclidienne suggère que les différences individuelles entre les caractéristiques sont plus importantes que leur magnitude globale dans ce contexte.
            </p>

            <h4>IV.5.2.2 Decision Tree</h4>
            <div class="code">
                <pre>
================================================================================
                            MÉTHODE : Decision Tree
================================================================================
______________Entraînement du modèle Decision Tree______________
Veuillez patienter quelques instants...
Le modèle Decision Tree a été entraîné avec les meilleurs hyperparamètres: {'max_depth': 3, 'max_features': np.float64(0.9183883618709039), 'min_samples_leaf': 4, 'min_samples_split': 19}.
La prédiction avec les données de test...
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2126
Racine de l'erreur quadratique (RMSE) : 0.4611
Erreur absolue moyenne (MAE)          : 0.4002
Coefficient de détermination (R²)     : 0.7908
Score de variance expliquée           : 0.7909
                </pre>
            </div>
            <p class="figure-caption">Figure 33: Résultats de l'entraînement et de l'évaluation du modèle Decision Tree</p>

            <p class="note">
                <strong>Description de la Figure 34:</strong> La visualisation des prédictions du modèle Decision Tree (non disponible actuellement) montrerait un nuage de points comparant les valeurs prédites et réelles de la satisfaction client. Contrairement au KNN, les prédictions d'un arbre de décision se regroupent en clusters distincts correspondant aux feuilles de l'arbre, créant un motif en "paliers" plutôt qu'un nuage continu. Avec une profondeur de 3, on observerait environ 8 valeurs de prédiction distinctes (2³ feuilles potentielles), chacune correspondant à un chemin spécifique dans l'arbre.
            </p>

            <p class="note">
                <strong>Description de la Figure 35:</strong> La visualisation de la structure de l'arbre de décision (non disponible actuellement) montrerait un diagramme hiérarchique avec une racine (top) se divisant en branches selon des règles de décision basées sur les variables les plus importantes (niveau d'expérience, heures de travail par jour, productivité moyenne). Chaque nœud indiquerait la variable utilisée pour la division, le seuil de cette division, le nombre d'échantillons dans le nœud et la valeur moyenne de satisfaction client. Avec une profondeur maximale de 3, l'arbre aurait au maximum 3 niveaux de décision, ce qui le rend facilement interprétable.
            </p>

            <p>
                Le modèle Decision Tree a obtenu un coefficient de détermination (R²) de 0.7908, légèrement supérieur
                au modèle KNN. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Profondeur maximale: 3</li>
                    <li>Nombre minimal d'échantillons pour diviser un nœud: 19</li>
                    <li>Nombre minimal d'échantillons dans une feuille: 4</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle Decision Tree:</strong> L'arbre de décision offre une meilleure performance que le KNN avec un R² de 0.7908. La profondeur limitée à 3 niveaux indique que le modèle reste relativement simple, ce qui est souvent préférable pour éviter le surapprentissage. Cette simplicité permet également une meilleure interprétabilité du modèle, car on peut facilement visualiser les règles de décision. Les variables les plus importantes pour les divisions sont probablement le niveau d'expérience, les heures de travail par jour et la productivité moyenne, comme l'indique l'analyse d'importance des variables.
            </p>

            <h4>IV.5.2.3 Random Forest</h4>
            <div class="code">
                <pre>
================================================================================
                            MÉTHODE : Random Forest
================================================================================
______________Entraînement du modèle Random Forest______________
Veuillez patienter quelques instants...
Le modèle Random Forest a été entraîné avec les meilleurs hyperparamètres: {'max_depth': 3, 'max_features': np.float64(0.9183883618709039), 'min_samples_leaf': 4, 'min_samples_split': 19, 'n_estimators': 113}.
La prédiction avec les données de test...
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2123
Racine de l'erreur quadratique (RMSE) : 0.4608
Erreur absolue moyenne (MAE)          : 0.3998
Coefficient de détermination (R²)     : 0.7911
Score de variance expliquée           : 0.7913
                </pre>
            </div>
            <p class="figure-caption">Figure 36: Résultats de l'entraînement et de l'évaluation du modèle Random Forest</p>

            <p class="note">
                <strong>Description de la Figure 37:</strong> La visualisation des prédictions du modèle Random Forest (non disponible actuellement) montrerait un nuage de points comparant les valeurs prédites et réelles de la satisfaction client. Contrairement à l'arbre de décision simple, les prédictions du Random Forest seraient plus continues et moins regroupées en paliers distincts, car elles représentent la moyenne des prédictions de 113 arbres différents. Cette moyenne d'ensemble produit une distribution plus lisse et plus proche de la diagonale (prédiction parfaite), avec une erreur quadratique moyenne (RMSE) de 0.4608, la plus faible parmi tous les modèles testés.
            </p>

            <div class="figure">
                <img src="images/random_forest_feature_importance.png" alt="Importance des variables dans Random Forest">
                <div class="figure-caption">Figure 38: Importance des variables dans le modèle Random Forest</div>
            </div>

            <p>
                Le modèle Random Forest a obtenu le meilleur coefficient de détermination (R²) de 0.7911 parmi les modèles
                présentés. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Nombre d'arbres: 113</li>
                    <li>Profondeur maximale: 3</li>
                    <li>Nombre minimal d'échantillons pour diviser un nœud: 19</li>
                    <li>Nombre minimal d'échantillons dans une feuille: 4</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle Random Forest:</strong> Le Random Forest est le modèle le plus performant avec un R² de 0.7911. Cette légère amélioration par rapport à l'arbre de décision simple s'explique par l'effet d'ensemble qui réduit la variance du modèle. En combinant 113 arbres de décision différents, chacun entraîné sur un sous-ensemble aléatoire des données, le Random Forest offre des prédictions plus stables et robustes. La profondeur limitée à 3 pour chaque arbre maintient un bon équilibre entre complexité et généralisation. Ce modèle excelle à capturer les relations non linéaires entre les variables tout en évitant le surapprentissage.
            </p>

            <h4>IV.5.2.4 Linear Regression</h4>
            <div class="code">
                <pre>
================================================================================
                          MÉTHODE : Linear regression
================================================================================
______________Régression Linéaire Multiple______________
Entraînement du modèle de régression linéaire multiple...
Modèle entraîné avec succès.
Prédiction avec le modèle entraîné...
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2136
Racine de l'erreur quadratique (RMSE) : 0.4621
Erreur absolue moyenne (MAE)          : 0.4013
Coefficient de détermination (R²)     : 0.7899
Score de variance expliquée           : 0.7901
                </pre>
            </div>
            <p class="figure-caption">Figure 39: Résultats de l'entraînement et de l'évaluation du modèle de régression linéaire</p>

            <p class="note">
                <strong>Description de la Figure 40:</strong> La visualisation des prédictions du modèle de régression linéaire (non disponible actuellement) montrerait un nuage de points comparant les valeurs prédites et réelles de la satisfaction client. Les points seraient distribués de manière relativement uniforme autour de la ligne diagonale, avec une dispersion similaire à celle du Random Forest (RMSE de 0.4621 contre 0.4608 pour le Random Forest). Contrairement aux arbres de décision, la régression linéaire produit des prédictions continues qui varient de manière linéaire avec les variables d'entrée, ce qui se traduirait par un nuage de points plus homogène sans regroupements distincts.
            </p>

            <p class="note">
                <strong>Description de la Figure 41:</strong> La visualisation des coefficients du modèle de régression linéaire (non disponible actuellement) montrerait un graphique à barres horizontales représentant l'importance et la direction de l'influence de chaque variable sur la satisfaction client. Les variables avec des coefficients positifs (comme niveau_experience_expert, productivite_moyenne) augmenteraient la satisfaction client, tandis que celles avec des coefficients négatifs (comme temps_par_client_minutes) la diminueraient. L'ampleur des barres indiquerait la force de l'influence de chaque variable, permettant d'identifier facilement les facteurs les plus déterminants pour la satisfaction client dans un modèle linéaire.
            </p>

            <p>
                Le modèle de régression linéaire a obtenu un coefficient de détermination (R²) de 0.7899, légèrement
                inférieur aux modèles basés sur les arbres, mais reste très performant. Ce modèle a l'avantage d'être
                simple et facilement interprétable.
            </p>

            <p class="note">
                <strong>Analyse du modèle de régression linéaire:</strong> Avec un R² de 0.7899, la régression linéaire offre une performance remarquablement proche des modèles plus complexes, malgré sa simplicité. Cela suggère que les relations entre les variables explicatives et la satisfaction client sont relativement linéaires. Ce modèle présente l'avantage majeur d'être facilement interprétable : chaque coefficient indique directement l'impact d'une unité de changement dans la variable correspondante sur la satisfaction client. Cette interprétabilité en fait un outil précieux pour la prise de décision, car il permet d'identifier clairement l'effet marginal de chaque facteur.
            </p>

            <h4>IV.5.2.5 Gradient Boosting</h4>
            <div class="code">
                <pre>
================================================================================
                          MÉTHODE : Gradient Boosting
================================================================================
______________Entraînement du modèle Gradient Boosting Regressor______________
Optimisation des hyperparamètres avec RandomizedSearchCV...
Fitting 5 folds for each of 20 candidates, totalling 100 fits
Meilleurs hyperparamètres : {'alpha': np.float64(0.17964325184672755), 'learning_rate': np.float64(0.06879485872574355), 'max_depth': 3, 'min_samples_leaf': 8, 'min_samples_split': 17, 'n_estimators': 286, 'subsample': np.float64(0.7085396127095583)}
Score de validation croisée (R²) : 0.7924
Prédiction avec le modèle optimal...
_________________Évaluation du modèle_________________
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2177
Racine de l'erreur quadratique (RMSE) : 0.4666
Erreur absolue moyenne (MAE)          : 0.4046
Coefficient de détermination (R²)     : 0.7858
Score de variance expliquée           : 0.7859
                </pre>
            </div>
            <p class="figure-caption">Figure 42: Résultats de l'entraînement et de l'évaluation du modèle Gradient Boosting</p>

            <p class="note">
                <strong>Description de la Figure 43:</strong> La visualisation des prédictions du modèle Gradient Boosting (non disponible actuellement) montrerait un nuage de points comparant les valeurs prédites et réelles de la satisfaction client. Le Gradient Boosting, avec son approche séquentielle d'amélioration des erreurs, produirait des prédictions très précises avec un R² de 0.7858. Les points seraient bien alignés le long de la diagonale, avec une dispersion légèrement plus importante que le Random Forest mais meilleure que les modèles individuels. L'algorithme excelle particulièrement dans la correction des erreurs des modèles précédents, ce qui se traduirait par une meilleure capture des patterns complexes dans les données.
            </p>

            <p>
                Le modèle Gradient Boosting a obtenu un coefficient de détermination (R²) de 0.7858. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Nombre d'estimateurs: 286</li>
                    <li>Taux d'apprentissage: 0.0688</li>
                    <li>Profondeur maximale: 3</li>
                    <li>Sous-échantillonnage: 0.7085</li>
                    <li>Paramètre de régularisation alpha: 0.1796</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle Gradient Boosting:</strong> Le Gradient Boosting obtient un R² de 0.7858, se positionnant entre le Random Forest et la régression linéaire. Cette méthode d'ensemble séquentielle construit 286 arbres de décision faibles, chacun corrigeant les erreurs du précédent. Le taux d'apprentissage relativement faible (0.0688) et le sous-échantillonnage (0.7085) contribuent à éviter le surapprentissage. Bien que légèrement moins performant que le Random Forest, ce modèle offre une excellente capacité de généralisation et est particulièrement efficace pour capturer les relations non linéaires complexes entre les variables.
            </p>

            <h4>IV.5.2.6 XGBoost</h4>
            <div class="code">
                <pre>
================================================================================
                               MÉTHODE : XGBoost
================================================================================
_________________Entraînement du modèle XGBoost pour la régression_________________
Veuillez patienter quelques instants...
Meilleurs hyperparamètres XGBoost : {'colsample_bytree': np.float64(0.6353970008207678), 'learning_rate': np.float64(0.06879485872574355), 'max_depth': 3, 'n_estimators': 89, 'subsample': np.float64(0.9533601546034454)}
Prédiction en cours avec le modèle XGBoost...
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2146
Racine de l'erreur quadratique (RMSE) : 0.4632
Erreur absolue moyenne (MAE)          : 0.4015
Coefficient de détermination (R²)     : 0.7889
Score de variance expliquée           : 0.7891
                </pre>
            </div>
            <p class="figure-caption">Figure 44: Résultats de l'entraînement et de l'évaluation du modèle XGBoost</p>

            <p class="note">
                <strong>Description de la Figure 45:</strong> La visualisation des prédictions du modèle XGBoost (non disponible actuellement) montrerait un nuage de points avec une excellente corrélation entre les valeurs prédites et réelles. XGBoost, étant une implémentation optimisée du gradient boosting, produirait des prédictions très précises avec un R² de 0.7889. Les points seraient étroitement distribués autour de la ligne diagonale, démontrant la capacité supérieure de XGBoost à capturer les patterns complexes tout en maintenant une bonne généralisation. La dispersion serait similaire à celle du Random Forest, confirmant sa position parmi les modèles les plus performants.
            </p>

            <p>
                Le modèle XGBoost a obtenu un coefficient de détermination (R²) de 0.7889, se classant parmi les meilleurs modèles. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Nombre d'estimateurs: 89</li>
                    <li>Taux d'apprentissage: 0.0688</li>
                    <li>Profondeur maximale: 3</li>
                    <li>Sous-échantillonnage des colonnes: 0.6354</li>
                    <li>Sous-échantillonnage des lignes: 0.9534</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle XGBoost:</strong> XGBoost démontre une excellente performance avec un R² de 0.7889, se positionnant comme le deuxième meilleur modèle après le Random Forest. Cette implémentation optimisée du gradient boosting utilise seulement 89 estimateurs (contre 286 pour le Gradient Boosting classique) tout en obtenant de meilleurs résultats, démontrant son efficacité algorithmique. Les techniques de régularisation intégrées (sous-échantillonnage des colonnes et des lignes) contribuent à sa robustesse. XGBoost est particulièrement apprécié en production pour sa rapidité d'exécution et sa capacité à gérer les données manquantes.
            </p>

            <h4>IV.5.2.7 Extra Trees</h4>
            <div class="code">
                <pre>
================================================================================
                             MÉTHODE : Extra trees
================================================================================
__________Entraînement du modèle ExtraTrees__________
Optimisation des hyperparamètres avec RandomizedSearchCV...
Meilleurs hyperparamètres : {'ccp_alpha': np.float64(0.007897630363511394), 'max_depth': 12, 'max_features': None, 'max_leaf_nodes': 72, 'min_samples_leaf': 11, 'min_samples_split': 18, 'n_estimators': 491}
Score de validation croisée (RMSE -): -0.4484
Prédiction avec le modèle optimal...
_________________Évaluation du modèle_________________
----- Rapport d'Évaluation de Régression -----
Erreur quadratique moyenne (MSE)      : 0.2142
Racine de l'erreur quadratique (RMSE) : 0.4628
Erreur absolue moyenne (MAE)          : 0.4016
Coefficient de détermination (R²)     : 0.7893
Score de variance expliquée           : 0.7894
                </pre>
            </div>
            <p class="figure-caption">Figure 46: Résultats de l'entraînement et de l'évaluation du modèle Extra Trees</p>

            <p class="note">
                <strong>Description de la Figure 47:</strong> La visualisation des prédictions du modèle Extra Trees (non disponible actuellement) montrerait un nuage de points avec une très bonne corrélation entre les valeurs prédites et réelles, similaire au Random Forest mais avec une variance légèrement plus élevée. Avec un R² de 0.7893, les points seraient bien distribués autour de la ligne diagonale. L'Extra Trees, utilisant des divisions aléatoires plutôt qu'optimales, produirait des prédictions légèrement plus variables que le Random Forest, mais cette randomisation supplémentaire contribue à une meilleure généralisation et à une réduction du surapprentissage.
            </p>

            <p>
                Le modèle Extra Trees a obtenu un coefficient de détermination (R²) de 0.7893, très proche du Random Forest. Les meilleurs hyperparamètres trouvés sont:
                <ul>
                    <li>Nombre d'estimateurs: 491</li>
                    <li>Profondeur maximale: 12</li>
                    <li>Nombre maximal de nœuds feuilles: 72</li>
                    <li>Nombre minimal d'échantillons dans une feuille: 11</li>
                    <li>Paramètre de complexité: 0.0079</li>
                </ul>
            </p>

            <p class="note">
                <strong>Analyse du modèle Extra Trees:</strong> L'Extra Trees (Extremely Randomized Trees) obtient un excellent R² de 0.7893, rivalisant avec le Random Forest. Cette variante introduit une randomisation supplémentaire en sélectionnant aléatoirement les seuils de division plutôt que de les optimiser. Avec 491 estimateurs et une profondeur maximale de 12, le modèle capture efficacement les patterns complexes tout en évitant le surapprentissage grâce à la randomisation. Cette approche offre l'avantage d'être plus rapide à entraîner que le Random Forest tout en maintenant des performances comparables, ce qui en fait un excellent choix pour les grands ensembles de données.
            </p>
        </div>

        <div class="subsection">
            <h3>IV.5.3 Analyse des Résultats</h3>

            <div class="figure">
                <img src="images/comparaison_modeles_regression_complete.png" alt="Comparaison complète des modèles de régression">
                <div class="figure-caption">Figure 48: Comparaison des performances de tous les modèles de régression (R²)</div>
            </div>

            <div class="figure">
                <img src="images/analyse_complete_modeles_regression.png" alt="Analyse complète 4 panneaux">
                <div class="figure-caption">Figure 49: Analyse complète des métriques de performance (R², MSE, RMSE, Classement)</div>
            </div>

            <div class="figure">
                <img src="images/metriques_detaillees_modeles.png" alt="Métriques détaillées">
                <div class="figure-caption">Figure 50: Comparaison détaillée de toutes les métriques de performance</div>
            </div>

            <div class="figure">
                <img src="images/radar_chart_top5_modeles.png" alt="Graphique radar top 5">
                <div class="figure-caption">Figure 51: Analyse radar des 5 meilleurs modèles (métriques normalisées)</div>
            </div>

            <div class="figure">
                <img src="images/analyse_ecart_performance.png" alt="Analyse des écarts de performance">
                <div class="figure-caption">Figure 52: Analyse de l'écart de performance par rapport au meilleur modèle</div>
            </div>

            <div class="figure">
                <img src="images/performance_vs_complexite.png" alt="Performance vs complexité">
                <div class="figure-caption">Figure 53: Analyse de l'efficacité - Performance vs Complexité des modèles</div>
            </div>

            <div class="figure">
                <img src="images/tableau_recapitulatif_performances.png" alt="Tableau récapitulatif">
                <div class="figure-caption">Figure 54: Tableau récapitulatif complet des performances de tous les modèles</div>
            </div>

            <h4>IV.5.3.1 Analyse Visuelle Complète des Performances</h4>
            <p>
                Les visualisations ci-dessus offrent une analyse exhaustive des performances de nos 7 modèles de régression:
            </p>

            <p>
                <strong>Interprétation des visualisations:</strong>
                <ul>
                    <li><strong>Figure 48:</strong> Le graphique principal montre clairement la hiérarchie des performances, avec Random Forest en tête suivi de très près par Decision Tree et Linear Regression</li>
                    <li><strong>Figure 49:</strong> L'analyse 4-panneaux révèle la cohérence entre toutes les métriques - les modèles performants en R² le sont aussi en MSE et RMSE</li>
                    <li><strong>Figure 50:</strong> La comparaison détaillée confirme que les différences entre modèles sont minimes mais consistantes</li>
                    <li><strong>Figure 51:</strong> Le graphique radar des top 5 modèles montre des profils très similaires, confirmant l'homogénéité des performances</li>
                    <li><strong>Figure 52:</strong> L'analyse des écarts révèle que même le "pire" modèle (KNN) n'est qu'à 0.008 points du meilleur</li>
                    <li><strong>Figure 53:</strong> Le graphique performance vs complexité montre que la régression linéaire offre le meilleur rapport simplicité/performance</li>
                    <li><strong>Figure 54:</strong> Le tableau récapitulatif synthétise toutes les métriques avec un code couleur pour faciliter l'interprétation</li>
                </ul>
            </p>

            <h4>IV.5.3.2 Tableau Récapitulatif des Performances</h4>
            <p>
                Voici un tableau récapitulatif des performances de tous les modèles évalués, classés par ordre de performance (R²):
            </p>

            <table>
                <tr>
                    <th>Rang</th>
                    <th>Modèle</th>
                    <th>MSE</th>
                    <th>RMSE</th>
                    <th>MAE</th>
                    <th>R²</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td><strong>Random Forest</strong></td>
                    <td>0.2123</td>
                    <td>0.4608</td>
                    <td>0.3998</td>
                    <td><strong>0.7911</strong></td>
                </tr>
                <tr>
                    <td>2</td>
                    <td><strong>Decision Tree</strong></td>
                    <td>0.2126</td>
                    <td>0.4611</td>
                    <td>0.4002</td>
                    <td><strong>0.7908</strong></td>
                </tr>
                <tr>
                    <td>3</td>
                    <td><strong>Linear Regression</strong></td>
                    <td>0.2136</td>
                    <td>0.4621</td>
                    <td>0.4013</td>
                    <td><strong>0.7899</strong></td>
                </tr>
                <tr>
                    <td>4</td>
                    <td><strong>Extra Trees</strong></td>
                    <td>0.2142</td>
                    <td>0.4628</td>
                    <td>0.4016</td>
                    <td><strong>0.7893</strong></td>
                </tr>
                <tr>
                    <td>5</td>
                    <td><strong>XGBoost</strong></td>
                    <td>0.2146</td>
                    <td>0.4632</td>
                    <td>0.4015</td>
                    <td><strong>0.7889</strong></td>
                </tr>
                <tr>
                    <td>6</td>
                    <td><strong>Gradient Boosting</strong></td>
                    <td>0.2177</td>
                    <td>0.4666</td>
                    <td>0.4046</td>
                    <td><strong>0.7858</strong></td>
                </tr>
                <tr>
                    <td>7</td>
                    <td><strong>KNN</strong></td>
                    <td>0.2205</td>
                    <td>0.4696</td>
                    <td>0.4059</td>
                    <td><strong>0.7831</strong></td>
                </tr>
            </table>
            <p class="figure-caption">Figure 55: Tableau comparatif complet des performances de tous les modèles de régression</p>

            <p>
                Observations clés sur l'ensemble des 7 modèles évalués:
                <ul>
                    <li><strong>Performance homogène:</strong> Tous les modèles ont obtenu des performances remarquablement similaires, avec des coefficients R² variant entre 0.7831 et 0.7911 (écart de seulement 0.008)</li>
                    <li><strong>Trio de tête:</strong> Random Forest (0.7911), Decision Tree (0.7908) et Linear Regression (0.7899) dominent le classement avec des performances très proches</li>
                    <li><strong>Modèles d'ensemble performants:</strong> Les méthodes d'ensemble (Random Forest, Extra Trees, XGBoost) figurent toutes dans le top 5, confirmant leur efficacité</li>
                    <li><strong>Surprise de la régression linéaire:</strong> Malgré sa simplicité, la régression linéaire se classe 3ème, démontrant que les relations sont largement linéaires</li>
                    <li><strong>Gradient Boosting en retrait:</strong> Contrairement aux attentes, le Gradient Boosting classique performe moins bien que XGBoost et les autres méthodes d'ensemble</li>
                    <li><strong>Erreurs faibles:</strong> Toutes les RMSE sont inférieures à 0.47, ce qui est excellent pour une variable cible variant de 70 à 95</li>
                </ul>
            </p>

            <h4>IV.5.3.3 Analyse Comparative Détaillée des 7 Méthodes de Régression</h4>

            <p>
                Après avoir évalué 7 modèles de régression différents pour prédire la satisfaction client, nous pouvons établir une analyse comparative complète de leurs forces et faiblesses respectives:
            </p>

            <table>
                <tr>
                    <th>Modèle</th>
                    <th>Forces</th>
                    <th>Faiblesses</th>
                    <th>Cas d'utilisation optimal</th>
                </tr>
                <tr>
                    <td><strong>Random Forest</strong><br>(1er: R² = 0.7911)</td>
                    <td>
                        <ul>
                            <li>Performance prédictive la plus élevée</li>
                            <li>Robuste aux valeurs aberrantes</li>
                            <li>Capture les relations non linéaires</li>
                            <li>Fournit l'importance des variables</li>
                            <li>Moins sujet au surapprentissage</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Moins interprétable que les modèles simples</li>
                            <li>Temps d'entraînement plus long</li>
                            <li>Nécessite plus de ressources computationnelles</li>
                        </ul>
                    </td>
                    <td>
                        Idéal pour les prédictions en production où la précision est primordiale. Excellent pour identifier les facteurs les plus influents sur la satisfaction client.
                    </td>
                </tr>
                <tr>
                    <td><strong>Decision Tree</strong><br>(2ème: R² = 0.7908)</td>
                    <td>
                        <ul>
                            <li>Performance excellente (très proche du Random Forest)</li>
                            <li>Facilement visualisable et interprétable</li>
                            <li>Capture les relations non linéaires</li>
                            <li>Rapide à entraîner et à exécuter</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Peut être instable</li>
                            <li>Risque de surapprentissage si non contraint</li>
                        </ul>
                    </td>
                    <td>
                        Excellent pour la communication avec les parties prenantes non techniques. Permet d'expliquer visuellement les règles de décision.
                    </td>
                </tr>
                <tr>
                    <td><strong>Linear Regression</strong><br>(3ème: R² = 0.7899)</td>
                    <td>
                        <ul>
                            <li>Performance remarquable malgré la simplicité</li>
                            <li>Interprétabilité maximale (coefficients directs)</li>
                            <li>Très rapide à entraîner et à exécuter</li>
                            <li>Facile à déployer et à maintenir</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Ne capture pas les relations non linéaires</li>
                            <li>Sensible aux valeurs aberrantes</li>
                            <li>Suppose l'indépendance des variables</li>
                        </ul>
                    </td>
                    <td>
                        Parfait pour comprendre l'impact marginal de chaque facteur. Idéal pour les analyses économiques et la prise de décision basée sur les coefficients.
                    </td>
                </tr>
                <tr>
                    <td><strong>Extra Trees</strong><br>(4ème: R² = 0.7893)</td>
                    <td>
                        <ul>
                            <li>Performance très proche du Random Forest</li>
                            <li>Plus rapide à entraîner que Random Forest</li>
                            <li>Excellente généralisation grâce à la randomisation</li>
                            <li>Robuste au surapprentissage</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Moins interprétable que les modèles simples</li>
                            <li>Variance légèrement plus élevée</li>
                        </ul>
                    </td>
                    <td>
                        Excellent choix pour les grands ensembles de données où la vitesse d'entraînement est importante tout en maintenant une haute performance.
                    </td>
                </tr>
                <tr>
                    <td><strong>XGBoost</strong><br>(5ème: R² = 0.7889)</td>
                    <td>
                        <ul>
                            <li>Implémentation optimisée du gradient boosting</li>
                            <li>Excellent équilibre performance/efficacité</li>
                            <li>Gestion native des données manquantes</li>
                            <li>Techniques de régularisation intégrées</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Complexité des hyperparamètres</li>
                            <li>Moins interprétable</li>
                            <li>Peut nécessiter plus de tuning</li>
                        </ul>
                    </td>
                    <td>
                        Particulièrement apprécié en production pour sa rapidité d'exécution et sa robustesse. Idéal pour les applications industrielles.
                    </td>
                </tr>
                <tr>
                    <td><strong>Gradient Boosting</strong><br>(6ème: R² = 0.7858)</td>
                    <td>
                        <ul>
                            <li>Excellente capacité de généralisation</li>
                            <li>Capture efficacement les relations complexes</li>
                            <li>Correction séquentielle des erreurs</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Plus lent que XGBoost</li>
                            <li>Sensible au surapprentissage</li>
                            <li>Nécessite un tuning minutieux</li>
                        </ul>
                    </td>
                    <td>
                        Utile quand on a le temps d'optimiser finement les hyperparamètres et qu'on privilégie la compréhension du processus d'apprentissage séquentiel.
                    </td>
                </tr>
                <tr>
                    <td><strong>KNN</strong><br>(7ème: R² = 0.7831)</td>
                    <td>
                        <ul>
                            <li>Simple à comprendre conceptuellement</li>
                            <li>Aucune hypothèse sur la distribution</li>
                            <li>Adapté aux frontières complexes</li>
                        </ul>
                    </td>
                    <td>
                        <ul>
                            <li>Performance la plus faible du groupe</li>
                            <li>Sensible à l'échelle des variables</li>
                            <li>Coûteux en calcul pour les grandes données</li>
                            <li>Difficile à interpréter</li>
                        </ul>
                    </td>
                    <td>
                        Utile pour les cas où les données sont irrégulièrement distribuées ou comme baseline de comparaison pour d'autres modèles.
                    </td>
                </tr>
            </table>
            <p class="figure-caption">Figure 56: Analyse comparative détaillée de toutes les méthodes de régression utilisées</p>

            <p class="success">
                <strong>Conclusion finale sur le choix du modèle:</strong> Après évaluation de 7 modèles différents, le Random Forest reste le champion avec un R² de 0.7911, mais l'écart minimal entre les modèles (0.008 entre le 1er et le 7ème) révèle une stabilité remarquable des performances. Nos recommandations stratégiques:
                <ul>
                    <li><strong>Pour la prédiction pure et la production:</strong> Random Forest (1er) ou Decision Tree (2ème), qui offrent le meilleur équilibre performance/robustesse</li>
                    <li><strong>Pour l'interprétabilité maximale:</strong> Régression Linéaire (3ème), qui malgré sa simplicité rivalise avec les modèles complexes</li>
                    <li><strong>Pour les grandes données et la vitesse:</strong> Extra Trees (4ème) ou XGBoost (5ème), optimisés pour l'efficacité computationnelle</li>
                    <li><strong>Pour l'apprentissage et la recherche:</strong> Gradient Boosting (6ème), excellent pour comprendre les mécanismes d'amélioration séquentielle</li>
                    <li><strong>Pour les cas spéciaux:</strong> KNN (7ème), utile comme baseline ou pour des distributions de données particulières</li>
                </ul>
                <strong>Insight majeur:</strong> La proximité exceptionnelle des performances suggère que les relations entre les variables explicatives et la satisfaction client sont largement linéaires et stables, ce qui explique pourquoi même la régression linéaire simple rivalise avec les modèles d'ensemble sophistiqués. Cette découverte est précieuse pour la gestion RH car elle indique que des règles simples et interprétables peuvent être aussi efficaces que des algorithmes complexes pour prédire la satisfaction client.
            </p>

            <h4>IV.5.3.4 Importance des Variables</h4>
            <p>
                L'analyse de l'importance des variables dans le modèle Random Forest nous permet d'identifier les facteurs
                qui influencent le plus la performance de satisfaction client:
            </p>

            <div class="figure">
                <img src="images/importance_variables_random_forest.png" alt="Importance des variables dans le modèle Random Forest">
                <div class="figure-caption">Figure 57: Importance des variables dans le modèle Random Forest</div>
            </div>

            <p>
                Les variables les plus influentes sont:
                <ol>
                    <li><strong>niveau_experience</strong>: Le niveau d'expérience des employés est le facteur le plus déterminant
                    pour la satisfaction client, confirmant l'importance de recruter et retenir des employés expérimentés.</li>
                    <li><strong>heures_travail_jour</strong>: Le nombre d'heures travaillées par jour influence significativement
                    la performance, suggérant qu'une gestion optimale des horaires est cruciale.</li>
                    <li><strong>productivite_moyenne</strong>: La productivité des employés est fortement corrélée à la satisfaction
                    client, soulignant l'importance de la formation et de l'efficacité opérationnelle.</li>
                    <li><strong>cout_horaire</strong>: Le coût horaire des employés est également un facteur important, indiquant
                    qu'investir dans des employés qualifiés (potentiellement mieux rémunérés) peut améliorer la satisfaction client.</li>
                </ol>
            </p>

            <h4>IV.5.3.5 Prédictions et Résidus</h4>
            <p>
                L'analyse des prédictions et des résidus du modèle Random Forest nous permet de vérifier la qualité de nos prédictions:
            </p>

            <div class="figure">
                <img src="images/predictions_vs_reels.png" alt="Valeurs prédites vs valeurs réelles">
                <div class="figure-caption">Figure 58: Comparaison des valeurs prédites et des valeurs réelles</div>
            </div>

            <p>
                Le graphique montre une bonne correspondance entre les valeurs prédites et les valeurs réelles, avec
                la plupart des points proches de la ligne diagonale (prédiction parfaite). Quelques écarts sont observés
                pour les valeurs extrêmes, ce qui est courant dans les modèles de régression.
            </p>

            <div class="figure">
                <img src="images/distribution_residus.png" alt="Distribution des résidus">
                <div class="figure-caption">Figure 59: Distribution des résidus du modèle Random Forest</div>
            </div>

            <p>
                La distribution des résidus est approximativement normale et centrée autour de zéro, ce qui indique
                que notre modèle ne présente pas de biais systématique dans ses prédictions.
            </p>
        </div>
    </div>

    <div class="section" id="clustering">
        <h2>IV.6 Analyse par Clustering (Méthodes Non Supervisées)</h2>

        <div class="subsection">
            <h3>IV.6.1 Sélection des Méthodes de Clustering</h3>
            <p>
                Après avoir exploré les méthodes supervisées, nous nous intéressons maintenant aux méthodes de clustering
                pour identifier des groupes naturels d'employés partageant des caractéristiques similaires.
            </p>

            <div class="code">
                <pre>
Puisque vous avez un problème de clustering
Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparé
1. K-means
2. K-Prototypes
3. KModes
4. C-Means
5. Fuzzy C-means
6. COP-KMeans
7. MiniBatch-KMeans
8. CLARANS
9. DBSCAN
10. HDBSCAN
11. Mean-Shift
12. Clustering hiérarchique agglomératif
13. Divisive Hierarchical Clustering
14. BIRCH
15. Agglomerative BIRCH
16. Spectral
17. OPTICS
18. Affinity Propagation
19. Density Peaks
20. Self-Organizing Maps
Votre choix : 1, 5, 9, 12, 20
                </pre>
            </div>

            <p class="note">
                <strong>Choix effectué:</strong> Nous avons sélectionné cinq méthodes de clustering complémentaires pour obtenir
                différentes perspectives sur nos données:
                <ul>
                    <li><strong>K-means (1)</strong>: Algorithme classique basé sur la distance euclidienne, efficace pour identifier des groupes sphériques</li>
                    <li><strong>Fuzzy C-means (5)</strong>: Permet aux employés d'appartenir partiellement à plusieurs clusters, reflétant mieux la réalité</li>
                    <li><strong>DBSCAN (9)</strong>: Basé sur la densité, capable d'identifier des clusters de forme arbitraire et de détecter les valeurs aberrantes</li>
                    <li><strong>Clustering hiérarchique agglomératif (12)</strong>: Crée une hiérarchie de clusters, permettant d'explorer différents niveaux de granularité</li>
                    <li><strong>Self-Organizing Maps (20)</strong>: Approche basée sur les réseaux de neurones, préservant la topologie des données</li>
                </ul>
            </p>
        </div>

        <div class="subsection">
            <h3>IV.6.2 Analyse des Résultats du Clustering K-means</h3>
            <p>
                Nous commençons par analyser les résultats de l'algorithme K-means, qui est l'une des méthodes de clustering
                les plus utilisées en raison de sa simplicité et de son efficacité.
            </p>

            <div class="code">
                <pre>
 MÉTHODE : K-means
--------------------------------------------------------------------------------
✔ Meilleur k : 2 | Score silhouette : 0.2260

 Description des clusters :
Cluster 0 : 6686 points (66.86%)
Cluster 1 : 3314 points (33.14%)
 Visualisation uniquement disponible pour 2D ou 3D.
                </pre>
            </div>
            <p class="figure-caption">Figure 28: Résultats de l'algorithme K-means avec le meilleur nombre de clusters</p>

            <p>
                L'algorithme K-means a identifié 2 clusters distincts dans nos données, avec un score de silhouette de 0.2260.
                Le premier cluster (Cluster 0) contient 66.86% des employés, tandis que le second (Cluster 1) en contient 33.14%.
            </p>

            <p class="note">
                <strong>Description de la Figure 28:</strong> La visualisation des clusters K-means (non disponible actuellement) montrerait une projection en 2D des données à l'aide de l'Analyse en Composantes Principales (PCA). Les points seraient colorés selon leur appartenance aux clusters, avec le Cluster 0 (Employés Performants) représentant 66.86% des points et le Cluster 1 (Employés en Développement) représentant 33.14% des points. Cette visualisation permettrait d'observer la séparation entre les deux groupes d'employés dans l'espace des caractéristiques.
            </p>

            <h4>IV.6.2.1 Statistiques Descriptives par Cluster</h4>
            <div class="code">
                <pre>
********************************************************************************
                            Analyse Post-clustering
********************************************************************************
▶ Statistiques descriptives par cluster
Cluster                                        0            1
heurestravailjour             count  6686.000000  3314.000000
                              mean      0.594016    -1.198429
                              std       0.594570     0.395379
                              min      -0.526221    -2.318558
                              25%       0.101097    -1.512007
...                                          ...          ...
satisfactionclientperformance min      -1.186919    -1.898347
                              25%       0.235938    -1.613776
                              50%       0.662795    -1.186919
                              75%       0.947367    -0.760062
                              max       1.658795     0.947367

[64 rows x 2 columns]
                </pre>
            </div>
            <p class="figure-caption">Figure 29: Statistiques descriptives des variables par cluster</p>

            <p>
                L'analyse des statistiques descriptives par cluster révèle des différences significatives entre les deux groupes:
                <ul>
                    <li>Le Cluster 0 (66.86% des employés) présente des valeurs plus élevées pour 'heures_travail_jour', 'productivite_moyenne',
                    'satisfaction_client_performance' et 'niveau_experience'</li>
                    <li>Le Cluster 1 (33.14% des employés) présente des valeurs plus faibles pour ces mêmes variables</li>
                </ul>
            </p>

            <p class="note">
                <strong>Description de la Figure 29:</strong> Les boxplots des variables numériques par cluster (non disponibles actuellement) montreraient la distribution des variables comme 'heures_travail_jour', 'productivite_moyenne', 'cout_horaire' et 'satisfaction_client_performance' pour chaque cluster. Ces visualisations révéleraient que le Cluster 0 (Employés Performants) présente des valeurs médianes plus élevées pour ces variables, tandis que le Cluster 1 (Employés en Développement) présente des valeurs médianes plus faibles. Les boxplots permettraient également d'observer la dispersion des données au sein de chaque cluster.
            </p>

            <h4>IV.6.2.2 Distribution des Variables Catégorielles par Cluster</h4>
            <p>
                L'analyse de la distribution des variables catégorielles par cluster nous permet d'identifier les caractéristiques
                distinctives de chaque groupe.
            </p>

            <p class="note">
                <strong>Description de la Figure 30:</strong> Les graphiques de distribution des variables catégorielles par cluster (non disponibles actuellement) montreraient la répartition des catégories comme 'poste', 'niveau_experience' et 'satisfaction_client' pour chaque cluster. Ces visualisations révéleraient que le Cluster 0 (Employés Performants) contient une proportion plus élevée d'employés avec un niveau d'expérience 'avancé' et 'expert', tandis que le Cluster 1 (Employés en Développement) contient davantage d'employés avec un niveau d'expérience 'débutant'. De même, la distribution des postes et de la satisfaction client qualitative varierait entre les clusters.
            </p>

            <p>
                Les principales différences entre les clusters en termes de variables catégorielles sont:
                <ul>
                    <li>Le Cluster 0 contient une proportion plus élevée d'employés avec un niveau d'expérience 'avancé' et 'expert'</li>
                    <li>Le Cluster 1 contient davantage d'employés avec un niveau d'expérience 'débutant'</li>
                    <li>Le Cluster 0 présente une satisfaction client qualitative majoritairement 'élevée'</li>
                    <li>La distribution des postes varie également entre les clusters, avec plus de baristas et de serveurs dans le Cluster 0</li>
                </ul>
            </p>

            <h4>IV.6.2.3 Interprétation des Clusters</h4>
            <p>
                Sur la base de notre analyse, nous pouvons interpréter les deux clusters comme suit:
            </p>

            <p class="note">
                <strong>Cluster 0 - "Employés Performants" (66.86%):</strong> Ce groupe se caractérise par des employés plus expérimentés,
                travaillant plus d'heures par jour, avec une productivité plus élevée et générant une meilleure satisfaction client.
                Ces employés représentent le cœur de la force de travail du café, contribuant significativement à la qualité du service
                et à la satisfaction des clients.
            </p>

            <p class="note">
                <strong>Cluster 1 - "Employés en Développement" (33.14%):</strong> Ce groupe comprend des employés moins expérimentés,
                travaillant moins d'heures, avec une productivité plus faible et générant une satisfaction client moins élevée.
                Ces employés pourraient bénéficier de formations supplémentaires et d'un encadrement plus étroit pour améliorer
                leurs performances.
            </p>
        </div>

        <p class="warning">
            <strong>Note:</strong> Les analyses détaillées des autres méthodes de clustering (Fuzzy C-means, DBSCAN, Clustering hiérarchique agglomératif, et Self-Organizing Maps) seront complétées ultérieurement pour offrir une perspective plus complète sur les groupes naturels d'employés.
        </p>
    </div>

    <div class="section" id="conclusion">
        <h2>IV.7 Conclusion et Recommandations</h2>

        <div class="subsection">
            <h3>IV.7.1 Synthèse des Résultats</h3>
            <p>
                Notre analyse des données de ressources humaines d'un café a permis de développer des modèles prédictifs
                performants pour anticiper la satisfaction client en fonction des caractéristiques des employés. Les
                principales conclusions sont:
            </p>
            <ul>
                <li>Les modèles de régression, en particulier Random Forest, peuvent prédire la performance de satisfaction
                client avec une précision élevée (R² ≈ 0.79)</li>
                <li>Le niveau d'expérience des employés, les heures de travail, la productivité et le coût horaire sont
                les facteurs les plus déterminants pour la satisfaction client</li>
                <li>Les variables liées aux tâches spécifiques et à la complexité ont moins d'impact sur la satisfaction client</li>
            </ul>
        </div>

        <div class="subsection">
            <h3>IV.7.2 Recommandations pour la Gestion des Ressources Humaines</h3>
            <p>
                Sur la base de notre analyse, nous recommandons les actions suivantes pour optimiser la gestion des
                ressources humaines et améliorer la satisfaction client:
            </p>
            <ol>
                <li><strong>Recrutement et rétention d'employés expérimentés</strong>: Prioriser l'embauche d'employés
                ayant de l'expérience dans le secteur de la restauration et mettre en place des stratégies de rétention
                pour les employés expérimentés.</li>
                <li><strong>Optimisation des horaires de travail</strong>: Ajuster les horaires de travail pour maximiser
                la performance tout en évitant la fatigue des employés. Les données suggèrent qu'il existe une relation
                non linéaire entre les heures travaillées et la satisfaction client.</li>
                <li><strong>Programmes de formation pour améliorer la productivité</strong>: Investir dans des programmes
                de formation pour augmenter la productivité moyenne des employés, qui est fortement corrélée à la
                satisfaction client.</li>
                <li><strong>Politique salariale adaptée</strong>: Considérer le coût horaire comme un investissement
                plutôt qu'une simple dépense, car des employés mieux rémunérés (et probablement plus qualifiés) contribuent
                à une meilleure satisfaction client.</li>
                <li><strong>Suivi régulier des indicateurs clés</strong>: Mettre en place un système de suivi des
                indicateurs identifiés comme importants (niveau d'expérience, productivité, satisfaction client) pour
                anticiper et résoudre rapidement les problèmes potentiels.</li>
            </ol>
        </div>

        <div class="subsection">
            <h3>IV.7.3 Perspectives Futures</h3>
            <p>
                Pour approfondir cette analyse et améliorer davantage la gestion des ressources humaines, nous suggérons:
            </p>
            <ul>
                <li>Collecter des données temporelles pour analyser l'évolution de la satisfaction client au fil du temps
                et identifier des tendances saisonnières</li>
                <li>Intégrer des données externes comme les conditions météorologiques, les événements locaux ou les
                périodes de vacances qui peuvent influencer l'affluence et les besoins en personnel</li>
                <li>Développer un système de prévision dynamique qui ajuste automatiquement les recommandations de
                personnel en fonction des prédictions de satisfaction client</li>
                <li>Réaliser des expérimentations contrôlées pour valider l'impact causal des facteurs identifiés comme
                importants (par exemple, tester différentes configurations d'horaires de travail)</li>
            </ul>
            <p>
                En mettant en œuvre ces recommandations et en poursuivant l'analyse des données, le café pourra optimiser
                sa gestion des ressources humaines, améliorer la satisfaction client et, par conséquent, augmenter sa
                rentabilité à long terme.
            </p>
        </div>
    </div>
</body>
</html>
