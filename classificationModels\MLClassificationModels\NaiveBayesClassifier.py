from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.naive_bayes import GaussianNB
from sklearn.metrics import accuracy_score
from evaluationModels.evaluation_classification import ClassifierEvaluator
from scipy.stats import uniform as sp_uniform

class Method_NaiveBayes_Classifier:
    def __init__(self):
        self.best_parameter = None

    
    def train_naive_bayes(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        print("Veuillez patienter quelques instants...")
        nb = GaussianNB()
        param_dist = {
            'var_smoothing': sp_uniform(1e-10, 1e-9)
        }
        random_search = RandomizedSearchCV(nb, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)
        self.best_parameter = random_search.best_estimator_
        print(f"Le modèle Naive Bayes a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")
        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné. Veuillez appeler la méthode 'train_naive_bayes' d'abord.")
        else:
            print("La prédiction avec les données de test...")
        return self.best_parameter.predict(X_test)

    def run_naive_bayes_classifier(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle Naive Bayes______________")
        self.train_naive_bayes(X_train, y_train)
        y_pred = self.predict(X_test) 
        # Évaluation du modèle
        print('_________________Evaluation Metrics_________________')
        accuracy = accuracy_score(y_test, y_pred)
        
       
        evaluator = ClassifierEvaluator(y_test, y_pred) 
        evaluator.evaluation_metrics()
        print(f'Accuracy: {accuracy * 100:.2f}%')

        
