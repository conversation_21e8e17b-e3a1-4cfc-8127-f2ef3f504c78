import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from minisom import MiniSom
from sklearn.metrics import silhouette_score
from sklearn.model_selection import ParameterGrid
from sklearn.metrics import euclidean_distances
from mpl_toolkits.mplot3d import Axes3D

class SOMClustering:
    def __init__(self, data, iterations=1000):
        self.data = data.values if isinstance(data, pd.DataFrame) else data
        self.iterations = iterations
        self.best_params = None
        self.best_score = -1
        self.som = None
        self.labels = None
        self.centroids = None

    def _generate_integer_labels(self, som):
        return np.array([
            winner[0] * som._weights.shape[1] + winner[1]
            for winner in [som.winner(x) for x in self.data]
        ])

    def _optimize_hyperparameters(self):
        print("Optimisation des hyperparamètres SOM...")
        param_grid = ParameterGrid({
            'x_dim': [5, 10],
            'y_dim': [5, 10],
            'sigma': [0.5, 1.0],
            'learning_rate': [0.3, 0.5]
        })

        for params in param_grid:
            try:
                som = MiniSom(
                    x=params['x_dim'],
                    y=params['y_dim'],
                    input_len=self.data.shape[1],
                    sigma=params['sigma'],
                    learning_rate=params['learning_rate']
                )
                som.train(self.data, self.iterations)

                labels = self._generate_integer_labels(som)
                if len(set(labels)) <= 1 or len(set(labels)) == len(self.data):
                    continue

                score = silhouette_score(self.data, labels)
                if score > self.best_score:
                    self.best_score = score
                    self.best_params = params
                    self.som = som
                    self.labels = labels
            except Exception as e:
                continue

        if self.best_params:
            print(f"✔ Meilleurs paramètres : {self.best_params}")
            print(f"✔ Meilleur Silhouette Score : {self.best_score:.4f}")
        else:
            print("Aucun clustering valable trouvé.")

    def _fit_model(self):
        if self.som is None:
            print("Aucun modèle SOM valide trouvé.")
        else:
            self.labels = self._generate_integer_labels(self.som)
            self.centroids = np.array([
                self.som.get_weights()[x, y]
                for x, y in [self.som.winner(x) for x in self.data]
            ])

    def _describe_clusters(self):
        if self.labels is None:
            print("Aucun cluster à décrire.")
            return
        unique_labels = set(self.labels)
        print("\nDescription des clusters :")
        for label in sorted(unique_labels):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        dims = self.data.shape[1]
        if dims not in [2, 3]:
            print("Visualisation uniquement disponible pour 2D ou 3D.")
            return

        centroids = self.centroids
        if dims == 2:
            plt.scatter(self.data[:, 0], self.data[:, 1], c=self.labels, cmap='tab10', s=40)
            plt.scatter(centroids[:, 0], centroids[:, 1], c='red', marker='x', s=100, label='Centroides')
            plt.title("Clustering SOM (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.legend()
            plt.grid(True)
            plt.show()
        elif dims == 3:
            fig = plt.figure()
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(self.data[:, 0], self.data[:, 1], self.data[:, 2], c=self.labels, cmap='tab10', s=40)
            ax.scatter(centroids[:, 0], centroids[:, 1], centroids[:, 2], c='red', s=100, label='Centroides')
            ax.set_title("Clustering SOM (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.legend()
            plt.show()

    def run_self_organizing_maps_clustering(self):
        self._optimize_hyperparameters()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
