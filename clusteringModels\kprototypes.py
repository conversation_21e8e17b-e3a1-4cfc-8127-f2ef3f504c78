import numpy as np

from kmodes.kprototypes import KPrototypes

import matplotlib.pyplot as plt
from mpl_toolkits.mplot3d import Axes3D

class KPrototypesClustering:
    def __init__(self, data, categorical_columns, max_clusters=10):
        self.data = data
        self.categorical_columns = categorical_columns  # indices des colonnes catégorielles
        self.max_clusters = min(max_clusters, len(data))  # S'assurer de ne pas dépasser le nombre de données
        self.best_k = None
        self.model = None
        self.labels = None
        self.centroids = None

    def _find_optimal_k(self):
        best_cost = float('inf')
        costs = []
        valid_ks = []

        for k in range(2, self.max_clusters + 1):
            try:
                model = KPrototypes(n_clusters=k, init='Cao', n_init=5, verbose=0)
                clusters = model.fit_predict(self.data, categorical=self.categorical_columns)
                costs.append(model.cost_)
                valid_ks.append(k)

                if model.cost_ < best_cost:
                    best_cost = model.cost_
                    self.best_k = k
            except ValueError as e:
                print(f"Échec pour k={k} : {e}")
                continue

        if self.best_k is None:
            raise RuntimeError("Impossible de trouver un k optimal pour K-Prototypes.")

        self._plot_cost(valid_ks, costs)
        print(f"Meilleur k : {self.best_k} | Coût : {best_cost:.4f}")


    def _fit_model(self):
        self.model = KPrototypes(n_clusters=self.best_k, init='Cao', n_init=5, verbose=0)
        self.labels = self.model.fit_predict(self.data, categorical=self.categorical_columns)
        self.centroids = self.model.cluster_centroids_

    def _describe_clusters(self):
        print("\nDescription des clusters :")
        for label in range(self.best_k):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _plot_cost(self, ks, costs):
        plt.plot(ks, costs, marker='o')
        plt.title("Coût du modèle (K-Prototypes)")
        plt.xlabel("k")
        plt.ylabel("Coût")
        plt.grid(True)
        plt.show()


    def run_k_prototypes_clustering(self):
        self._find_optimal_k()
        self._fit_model()
        self._describe_clusters()

