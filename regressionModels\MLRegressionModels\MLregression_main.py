from regressionModels.MLRegressionModels.KNNRegressor import Method_KNN_Regressor
from regressionModels.MLRegressionModels.DecisionTreeRegressor import Method_DecisionTree
from regressionModels.MLRegressionModels.RandomForestRegressor import Method_RandomForest
from regressionModels.MLRegressionModels.LinearRegressor import Method_Linear_Regressor
from regressionModels.MLRegressionModels.BayesianLinearRegressor import Method_BayesianLinear_Regressor
from regressionModels.MLRegressionModels.BayesianRidgeRegressor import Method_BayesianRidge_Regressor
from regressionModels.MLRegressionModels.GaussianProcessRegressor import Method_GaussianProcess_Regressor
from regressionModels.MLRegressionModels.GradientBoostingRegressor import Method_GradientBoosting_Regressor
from regressionModels.MLRegressionModels.AdaBoostRegressor import Method_AdaBoost_Regressor
from regressionModels.MLRegressionModels.SupportVectorRegressor import Method_SVR_Regressor
from regressionModels.MLRegressionModels.XGBoostRegressor import Method_XGBoost_Regressor
from regressionModels.MLRegressionModels.CatBoostRegressor import Method_Catboost_Regressor
from regressionModels.MLRegressionModels.ExtraTreesRegressor import Method_ExtraTrees_Regressor
from regressionModels.MLRegressionModels.LightGBMRegressor import Method_LightGBM_Regressor

class RegressorMLModels_main:
    def __init__(self):
        model_list = [
            ('KNN', Method_KNN_Regressor()),
            ('Decision Tree', Method_DecisionTree()),
            ('Random Forest', Method_RandomForest()),
            ('SVR', Method_SVR_Regressor()),
            ('Linear regression', Method_Linear_Regressor()),
            ('Bayesian linear', Method_BayesianLinear_Regressor()),
            ('Bayesian Ridge', Method_BayesianRidge_Regressor()),
            ('Gaussian Process regression', Method_GaussianProcess_Regressor()),
            ('Gradient Boosting', Method_GradientBoosting_Regressor()),
            ('AdaBoost', Method_AdaBoost_Regressor()),
            ('XGBoost',Method_XGBoost_Regressor()),
            ('Catboost', Method_Catboost_Regressor()),
            ('Extra trees', Method_ExtraTrees_Regressor()),
            ('LightGBM', Method_LightGBM_Regressor())
        ]
        self.models = {i + 1: model for i, model in enumerate(model_list)}

    def select_models(self):
        print("\n" + "="*80)
        print("Construction et évaluation des modèles de machine learning".center(80))
        print("="*80)
        print("Puisque vous avez un problème de régression")
        print("Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparés par des virgules (e.g., 1, 3):")

        while True:
            for idx, (name, _) in self.models.items():
                print(f"{idx}. {name}")

            selected_indices = input("Votre choix : ").split(',')
            selected_indices = [
                int(idx.strip()) for idx in selected_indices 
                if idx.strip().isdigit() and int(idx.strip()) in self.models
            ]

            if selected_indices:
                return selected_indices
            else:
                print("Erreur : votre choix est invalide. Veuillez réessayer.")

    def models_selected_regressor(self, model_indices, X_train, y_train, X_test, y_test):
        for idx in model_indices:
            model_name, model = self.models[idx]

            method_name = f"run_{model_name.lower().replace(' ', '_')}_regressor"

            if hasattr(model, method_name):
                print("\n" + "="*80)
                print(f"MÉTHODE : {model_name}".center(80))
                print("="*80)
                getattr(model, method_name)(X_train, y_train, X_test, y_test)
                print("\n" + "-"*80)
                print(f"Processus '{model_name}' terminé avec succès.".center(80))
                print("-"*80 + "\n")
                print("="*80 + "\n")
            else:
                print(f"Méthode '{method_name}' introuvable pour le modèle {model_name}")

    def run_models_selected_regressor(self, X_train, y_train, X_test, y_test):
        selected_indices = self.select_models()
        self.models_selected_regressor(selected_indices, X_train, y_train, X_test, y_test)
