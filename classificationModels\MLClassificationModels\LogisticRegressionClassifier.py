from sklearn.linear_model import LogisticRegression
from scipy.stats import loguniform
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from evaluationModels.evaluation_classification import ClassifierEvaluator


class Method_LogisticRegression_Classifier:
    def __init__(self):
        self.best_parameter = None

    def train_logistic(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'C': loguniform(1e-4, 1e4),  # Paramètre de régularisation inverse
            'penalty': ['l1', 'l2'],      # Type de régularisation
            'solver': ['liblinear'],       # Algorithme d'optimisation
            'max_iter': [1000]             # Itérations maximales
        }

        model = LogisticRegression()

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(accuracy_score),
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_logistic_regression_classifier(self, X_train, y_train, X_test, y_test):
        print("______ Entraînement de la régression logistique ______")
        self.train_logistic(X_train, y_train)
        y_pred = self.predict(X_test)
        print('_________________Évaluation du modèle_________________')
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()