import pandas as pd
import os
import io
import csv
import zipfile
import tkinter as tk
from tkinter import filedialog, simpledialog


class DataImporter:
    def __init__(self):
        self.root = tk.Tk()
        self.root.withdraw()
        # إضافة هذه السطور لضمان ظهور نافذة اختيار الملفات
        self.root.wm_attributes('-topmost', 1)
        self.root.update()

    def detect_delimiter(self, filename):
        try:
            with open(filename, 'r') as file:
                content = file.read(1024)
                sniffer = csv.Sniffer()
                dialect = sniffer.sniff(content, delimiters=[',', '\t', ';', '|', ' '])
                return dialect.delimiter
        except csv.Error:
            delimiters = [',', '\t', ';', '|', ' ']
            for delim in delimiters:
                if delim in content:
                    return delim
            print("Aucun délimiteur standard détecté.")
            delim = simpledialog.askstring("Entrée requise", "Entrez le séparateur utilisé dans le fichier :")
            if delim:
                return delim
            else:
                print("Aucun séparateur fourni. Impossible de continuer.")
                return None

    def import_data_file(self, delim=None):
        print("\n" + "="*80)
        print("Chargement des données".center(80))
        print("="*80)

        # أولاً، اسأل المستخدم عما إذا كان يريد استخدام مجموعة بيانات افتراضية
        print("Options de chargement des données:")
        print("1. Utiliser un jeu de données par défaut (Iris)")
        print("2. Sélectionner un fichier")
        print("3. Entrer le chemin du fichier manuellement")

        choice = input("Votre choix (1-3): ").strip()

        # استخدام مجموعة بيانات افتراضية
        if choice == '1':
            print("Utilisation du jeu de données Iris...")
            from sklearn.datasets import load_iris
            iris = load_iris()
            data = pd.DataFrame(data=iris.data, columns=iris.feature_names)
            # إضافة عمود الهدف
            data['target'] = iris.target
            return data

        # اختيار ملف باستخدام مربع حوار
        elif choice == '2':
            try:
                # إعادة تنشيط النافذة قبل فتح مربع الحوار
                self.root.update()

                print("Veuillez sélectionner un fichier de données (CSV, Excel, ou TXT)...")

                chemin_fichier = filedialog.askopenfilename(
                    parent=self.root,
                    title="Sélectionnez un fichier de données",
                    filetypes=[("Fichiers CSV", "*.csv"), ("Fichiers Excel", "*.xlsx"), ("Fichiers texte", "*.txt")])

                # إعادة تنشيط النافذة بعد اختيار الملف
                self.root.update()

                if not chemin_fichier:
                    print("Aucun fichier sélectionné")
                    return self._use_default_data()
            except Exception as e:
                print(f"Erreur lors de l'ouverture du sélecteur de fichiers: {str(e)}")
                return self._use_default_data()

        # إدخال مسار الملف يدويًا
        elif choice == '3':
            chemin_fichier = input("Entrez le chemin complet du fichier: ").strip()
            if not os.path.exists(chemin_fichier):
                print(f"Le fichier {chemin_fichier} n'existe pas.")
                return self._use_default_data()
        else:
            print("Choix invalide.")
            return self._use_default_data()

        print(f"Fichier sélectionné: {chemin_fichier}")

        extension_fichier = os.path.splitext(chemin_fichier)[1].lower()

        try:
            if extension_fichier == '.csv':
                if not delim:
                    delim = self.detect_delimiter(chemin_fichier)
                print(f"Lecture du fichier CSV avec délimiteur: {delim}")
                return pd.read_csv(chemin_fichier, delimiter=delim)

            elif extension_fichier == '.txt':
                print("Lecture du fichier texte...")
                with open(chemin_fichier, 'r') as file:
                    data = file.read()
                return data

            elif extension_fichier == '.xlsx':
                print("Lecture du fichier Excel...")
                return pd.read_excel(chemin_fichier)

            else:
                print(f"Extension de fichier non prise en charge: {extension_fichier}")
                return self._use_default_data()

        except Exception as e:
            print(f"Erreur lors de la lecture du fichier: {str(e)}")
            return self._use_default_data()

    def _use_default_data(self):
        """استخدام مجموعة بيانات افتراضية"""
        print("Utilisation du jeu de données Iris par défaut...")
        from sklearn.datasets import load_iris
        iris = load_iris()
        data = pd.DataFrame(data=iris.data, columns=iris.feature_names)
        # إضافة عمود الهدف
        data['target'] = iris.target
        return data


