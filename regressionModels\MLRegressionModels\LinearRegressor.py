from sklearn.linear_model import LinearRegression
from evaluationModels.evaluation_regressor import RegressionEvaluator


class Method_Linear_Regressor:
    def __init__(self):
        # Attribut pour enregistrer le modèle entraîné
        self.model = None

    def train_linear_regression(self, X_train, y_train):

        print("Entraînement du modèle de régression linéaire multiple...")

        model = LinearRegression()

        model.fit(X_train, y_train)

        self.model = model

        print("Modèle entraîné avec succès.")
        return self

    def predict(self, X_test):
        if self.model is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle entraîné...")
        return self.model.predict(X_test)

    def run_linear_regression_regressor(self, X_train, y_train, X_test, y_test):

        print("______________Régression Linéaire Multiple______________")

        self.train_linear_regression(X_train, y_train)

        y_pred = self.predict(X_test)

        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
