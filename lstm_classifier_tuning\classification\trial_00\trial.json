{"trial_id": "00", "hyperparameters": {"space": [{"class_name": "Int", "config": {"name": "units", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "num_layers", "default": null, "conditions": [], "min_value": 1, "max_value": 3, "step": 1, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "units_0", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "dense_units", "default": null, "conditions": [], "min_value": 64, "max_value": 256, "step": 64, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.0001, "conditions": [], "min_value": 0.0001, "max_value": 0.01, "step": null, "sampling": "log"}}, {"class_name": "Int", "config": {"name": "units_1", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}], "values": {"units": 128, "num_layers": 2, "units_0": 32, "dense_units": 192, "learning_rate": 0.009971878258105844, "units_1": 32}}, "metrics": {"metrics": {"accuracy": {"direction": "max", "observations": [{"value": [0.5929203430811564], "step": 9}]}, "loss": {"direction": "min", "observations": [{"value": [-134114284.0], "step": 9}]}, "val_accuracy": {"direction": "max", "observations": [{"value": [0.0], "step": 9}]}, "val_loss": {"direction": "min", "observations": [{"value": [-1884431936.0], "step": 9}]}}}, "score": -1884431936.0, "best_step": 9, "status": "COMPLETED", "message": null}