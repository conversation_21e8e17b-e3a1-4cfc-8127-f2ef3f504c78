import matplotlib.pyplot as plt
import numpy as np
import pandas as pd
import seaborn as sns
from matplotlib.patches import Patch

# Set style
plt.style.use('default')
sns.set_palette("viridis")

# Data for clustering methods comparison
methods = ['K-means', 'K-Prototypes', 'HDBSCAN', 'MiniBatch K-means', 'COP-KMeans', 'DBSCAN']
n_clusters = [2, 10, 2, 2, 2, 17]
silhouette_scores = [0.2260, None, 0.1668, 0.2260, 0.2260, -0.3428]
noise_percentage = [0, 0, 0.35, 0, 0, 99.49]
execution_time = [1.0, 2.5, 3.0, 0.3, 1.0, 1.5]  # Relative execution times
interpretability = [8, 6, 5, 8, 7, 3]  # Subjective interpretability score (1-10)

print("Creating clustering comparison visualizations...")

# 1. Number of clusters comparison
plt.figure(figsize=(12, 6))
colors = ['#2E8B57', '#4682B4', '#DAA520', '#CD853F', '#9370DB', '#DC143C']
bars = plt.bar(methods, n_clusters, color=colors, alpha=0.8)

# Add value labels on bars
for i, (bar, clusters) in enumerate(zip(bars, n_clusters)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.3, 
             f'{clusters}', ha='center', va='bottom', fontweight='bold', fontsize=11)

plt.title('Comparaison du Nombre de Clusters Identifiés', fontsize=14, fontweight='bold')
plt.ylabel('Nombre de Clusters', fontsize=12)
plt.xlabel('Méthodes de Clustering', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('images/clustering_nb_clusters_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 71 created: clustering_nb_clusters_comparison.png")

# 2. Silhouette scores comparison (excluding None values)
plt.figure(figsize=(12, 6))
methods_with_silhouette = [m for i, m in enumerate(methods) if silhouette_scores[i] is not None]
scores_filtered = [s for s in silhouette_scores if s is not None]
colors_filtered = [colors[i] for i, s in enumerate(silhouette_scores) if s is not None]

bars = plt.bar(methods_with_silhouette, scores_filtered, color=colors_filtered, alpha=0.8)

# Add value labels on bars
for i, (bar, score) in enumerate(zip(bars, scores_filtered)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01 if score > 0 else bar.get_height() - 0.03, 
             f'{score:.4f}', ha='center', va='bottom' if score > 0 else 'top', 
             fontweight='bold', fontsize=11)

plt.title('Comparaison des Scores de Silhouette', fontsize=14, fontweight='bold')
plt.ylabel('Score de Silhouette', fontsize=12)
plt.xlabel('Méthodes de Clustering', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='Seuil de qualité')
plt.grid(axis='y', alpha=0.3)
plt.legend()
plt.tight_layout()
plt.savefig('images/clustering_silhouette_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 72 created: clustering_silhouette_comparison.png")

# 3. Noise percentage comparison
plt.figure(figsize=(12, 6))
bars = plt.bar(methods, noise_percentage, color=colors, alpha=0.8)

# Add value labels on bars
for i, (bar, noise) in enumerate(zip(bars, noise_percentage)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1, 
             f'{noise:.1f}%', ha='center', va='bottom', fontweight='bold', fontsize=11)

plt.title('Comparaison du Pourcentage de Bruit Détecté', fontsize=14, fontweight='bold')
plt.ylabel('Pourcentage de Bruit (%)', fontsize=12)
plt.xlabel('Méthodes de Clustering', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.yscale('log')  # Log scale due to DBSCAN's high noise percentage
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('images/clustering_noise_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 73 created: clustering_noise_comparison.png")

# 4. Performance radar chart for top 4 methods (excluding DBSCAN and K-Prototypes)
from math import pi

# Select top 4 methods for radar chart
top_methods = ['K-means', 'HDBSCAN', 'MiniBatch K-means', 'COP-KMeans']
top_indices = [0, 2, 3, 4]

# Normalize metrics for radar chart (higher is better for all)
silhouette_norm = [(silhouette_scores[i] + 0.5) / 1.0 for i in top_indices]  # Normalize to 0-1
speed_norm = [1.0 / execution_time[i] for i in top_indices]  # Inverse for speed (higher is better)
speed_norm = [s / max(speed_norm) for s in speed_norm]  # Normalize to 0-1
interpretability_norm = [interpretability[i] / 10.0 for i in top_indices]  # Normalize to 0-1
stability_norm = [0.9, 0.7, 0.9, 0.9]  # Subjective stability scores

# Categories for radar chart
categories = ['Qualité\n(Silhouette)', 'Vitesse\n(Exécution)', 'Interprétabilité', 'Stabilité']
N = len(categories)

# Compute angle for each axis
angles = [n / float(N) * 2 * pi for n in range(N)]
angles += angles[:1]

# Create radar chart
fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

# Colors for methods
radar_colors = ['#2E8B57', '#DAA520', '#CD853F', '#9370DB']

for i, method in enumerate(top_methods):
    values = [silhouette_norm[i], speed_norm[i], interpretability_norm[i], stability_norm[i]]
    values += values[:1]
    
    ax.plot(angles, values, 'o-', linewidth=2, label=method, color=radar_colors[i])
    ax.fill(angles, values, alpha=0.25, color=radar_colors[i])

# Add category labels
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories, fontsize=11)
ax.set_ylim(0, 1)
ax.set_title('Comparaison Radar des Méthodes de Clustering\n(Métriques Normalisées)', 
             size=14, fontweight='bold', pad=20)
ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
ax.grid(True)

plt.tight_layout()
plt.savefig('images/clustering_radar_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 74 created: clustering_radar_comparison.png")

# 5. Execution time vs Quality scatter plot
plt.figure(figsize=(10, 7))

# Filter out methods without silhouette scores for this plot
valid_indices = [i for i, s in enumerate(silhouette_scores) if s is not None]
valid_methods = [methods[i] for i in valid_indices]
valid_silhouette = [silhouette_scores[i] for i in valid_indices]
valid_execution = [execution_time[i] for i in valid_indices]
valid_colors = [colors[i] for i in valid_indices]

scatter = plt.scatter(valid_execution, valid_silhouette, s=200, c=valid_colors, 
                     alpha=0.7, edgecolors='black', linewidth=2)

# Add method labels
for i, method in enumerate(valid_methods):
    plt.annotate(method, (valid_execution[i], valid_silhouette[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=10, fontweight='bold')

plt.xlabel('Temps d\'Exécution Relatif', fontsize=12)
plt.ylabel('Score de Silhouette', fontsize=12)
plt.title('Analyse Efficacité: Vitesse vs Qualité du Clustering', fontsize=14, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.axhline(y=0, color='red', linestyle='--', alpha=0.7, label='Seuil de qualité')
plt.legend()
plt.tight_layout()
plt.savefig('images/clustering_efficiency_analysis.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 75 created: clustering_efficiency_analysis.png")

# 6. Comprehensive comparison table as image
fig, ax = plt.subplots(figsize=(14, 8))
ax.axis('tight')
ax.axis('off')

# Create comprehensive table
table_data = {
    'Méthode': methods,
    'Nb Clusters': n_clusters,
    'Silhouette': [f'{s:.4f}' if s is not None else 'N/A' for s in silhouette_scores],
    'Bruit (%)': [f'{n:.1f}%' for n in noise_percentage],
    'Vitesse': ['Rapide', 'Moyenne', 'Lente', 'Très Rapide', 'Rapide', 'Moyenne'],
    'Interprétabilité': ['Élevée', 'Moyenne', 'Faible', 'Élevée', 'Élevée', 'Très Faible'],
    'Recommandation': ['✅ Standard', '✅ Données Mixtes', '✅ Robustesse', '✅ Performance', '⚠️ Avec Contraintes', '❌ Inadapté']
}

df = pd.DataFrame(table_data)
table = ax.table(cellText=df.values, colLabels=df.columns, cellLoc='center', loc='center')
table.auto_set_font_size(False)
table.set_fontsize(10)
table.scale(1.2, 2)

# Color code the recommendations
for i in range(len(methods)):
    if '✅' in table_data['Recommandation'][i]:
        table[(i+1, 6)].set_facecolor('#90EE90')  # Light green
    elif '⚠️' in table_data['Recommandation'][i]:
        table[(i+1, 6)].set_facecolor('#FFE4B5')  # Light orange
    elif '❌' in table_data['Recommandation'][i]:
        table[(i+1, 6)].set_facecolor('#FFB6C1')  # Light red

# Highlight best performers
table[(1, 0)].set_facecolor('#90EE90')  # K-means
table[(2, 0)].set_facecolor('#90EE90')  # K-Prototypes
table[(4, 0)].set_facecolor('#90EE90')  # MiniBatch K-means

plt.title('Tableau Comparatif Complet des Méthodes de Clustering', 
          fontsize=14, fontweight='bold', pad=20)
plt.savefig('images/clustering_comprehensive_table.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 76 created: clustering_comprehensive_table.png")

# 7. Cluster distribution comparison for main methods
fig, axes = plt.subplots(2, 2, figsize=(14, 10))

# K-means distribution
ax1 = axes[0, 0]
kmeans_sizes = [6686, 3314]
kmeans_labels = ['Cluster 0\n(66.86%)', 'Cluster 1\n(33.14%)']
ax1.pie(kmeans_sizes, labels=kmeans_labels, autopct='%1.1f%%', startangle=90, colors=['#2E8B57', '#87CEEB'])
ax1.set_title('K-means\n(2 clusters)', fontweight='bold')

# K-Prototypes distribution (top 4 clusters)
ax2 = axes[0, 1]
kproto_sizes = [1788, 1748, 1137, 1110]
kproto_labels = ['Cluster 1\n(17.88%)', 'Cluster 8\n(17.48%)', 'Cluster 2\n(11.37%)', 'Cluster 7\n(11.10%)']
ax2.pie(kproto_sizes, labels=kproto_labels, autopct='%1.1f%%', startangle=90, colors=['#4682B4', '#DAA520', '#CD853F', '#9370DB'])
ax2.set_title('K-Prototypes\n(Top 4 des 10 clusters)', fontweight='bold')

# HDBSCAN distribution
ax3 = axes[1, 0]
hdbscan_sizes = [5258, 4707, 35]
hdbscan_labels = ['Cluster 0\n(52.58%)', 'Cluster 1\n(47.07%)', 'Bruit\n(0.35%)']
ax3.pie(hdbscan_sizes, labels=hdbscan_labels, autopct='%1.1f%%', startangle=90, colors=['#DAA520', '#CD853F', '#DC143C'])
ax3.set_title('HDBSCAN\n(2 clusters + bruit)', fontweight='bold')

# DBSCAN distribution
ax4 = axes[1, 1]
dbscan_sizes = [9949, 51]  # 9949 noise + 51 total in clusters (17*3)
dbscan_labels = ['Bruit\n(99.49%)', 'Clusters\n(0.51%)']
ax4.pie(dbscan_sizes, labels=dbscan_labels, autopct='%1.1f%%', startangle=90, colors=['#DC143C', '#FFB6C1'])
ax4.set_title('DBSCAN\n(17 micro-clusters)', fontweight='bold')

plt.suptitle('Comparaison des Distributions de Clusters', fontsize=16, fontweight='bold')
plt.tight_layout()
plt.savefig('images/clustering_distributions_comparison.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 77 created: clustering_distributions_comparison.png")

print("\n🎯 All clustering comparison visualizations created successfully!")
print("\n📊 Images generated:")
print("1. clustering_nb_clusters_comparison.png - Nombre de clusters")
print("2. clustering_silhouette_comparison.png - Scores de silhouette")
print("3. clustering_noise_comparison.png - Pourcentage de bruit")
print("4. clustering_radar_comparison.png - Comparaison radar")
print("5. clustering_efficiency_analysis.png - Efficacité vitesse vs qualité")
print("6. clustering_comprehensive_table.png - Tableau comparatif complet")
print("7. clustering_distributions_comparison.png - Distributions des clusters")

print(f"\n🏆 Classement des méthodes:")
print("1. K-means/MiniBatch K-means: Meilleur équilibre qualité/simplicité")
print("2. K-Prototypes: Meilleur pour données mixtes et analyse détaillée")
print("3. HDBSCAN: Meilleur pour robustesse et détection d'outliers")
print("4. COP-KMeans: Potentiel pour contraintes métier")
print("5. DBSCAN: Inadapté à ce type de données")
