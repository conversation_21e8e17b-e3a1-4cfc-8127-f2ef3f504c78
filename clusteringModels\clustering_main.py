import numpy as np
import pandas as pd
from clusteringModels.kmeans import KMeansClustering
from clusteringModels.kprototypes import KPrototypesClustering
from clusteringModels.kmodes import KModesClustering
from clusteringModels.cmeans import CMeansClustering
from clusteringModels.fuzzy_cmeans import FuzzyCMeansClustering
from clusteringModels.cop_kmeans import COPKMeansClustering
# Comment out models that require sklearn_extra
# from clusteringModels.kmedoids import KMedoidsClustering
from clusteringModels.clarans import CLARANSClustering
from clusteringModels.DBSCAN import DBSCANClustering
from clusteringModels.hdbscan import HDBSCANClustering
from clusteringModels.meanshift import MeanShiftClustering
from clusteringModels.agglomerative import Agglomerative_Clustering
from clusteringModels.divisivehierarchical import DivisiveHierarchicalClustering
from clusteringModels.birch import BIRCHClustering
from clusteringModels.Agglomerativebirch import AgglomerativeBirchClustering
from clusteringModels.spectral import SpectralClusteringModel
from clusteringModels.minibatchkmeans import MiniBatchKMeansClustering
from clusteringModels.OPTICS import OPTICSClustering
from clusteringModels.AffinityPropagation import AffinityPropagationClustering
from clusteringModels.DensityPeaks import DensityPeaksClustering
from clusteringModels.SelfOrganizingMaps import SOMClustering

from clusteringModels.ClusterAnalyzerVis import ClusterAnalyzer

class ClusteringModels_main:
    def __init__(self, data_encoded, data_original):
        if not isinstance(data_encoded, pd.DataFrame):
            data_encoded = pd.DataFrame(data_encoded)

        self.data_encoded = data_encoded
        self.data_original = data_original

        categorical_columns = [i for i, dtype in enumerate(data_encoded.dtypes) if dtype == 'object']

        model_list = [
            ('K-means', KMeansClustering(self.data_encoded)),
            ('K-Prototypes', KPrototypesClustering(self.data_encoded, categorical_columns)),
            ('KModes', KModesClustering(self.data_encoded)),
            ('C-Means', CMeansClustering(self.data_encoded)),
            ('Fuzzy C-means', FuzzyCMeansClustering(self.data_encoded)),
            ('COP-KMeans', COPKMeansClustering(self.data_encoded)),
            ('MiniBatch-KMeans', MiniBatchKMeansClustering(self.data_encoded)),
            # Comment out models that require sklearn_extra
            # ('K-Medoids', KMedoidsClustering(self.data_encoded,)),
            ('CLARANS', CLARANSClustering(self.data_encoded)),
            ('DBSCAN', DBSCANClustering(self.data_encoded)),
            ('HDBSCAN', HDBSCANClustering(self.data_encoded)),
            ('Mean-Shift', MeanShiftClustering()),
            ('Clustering hiérarchique agglomératif', Agglomerative_Clustering(self.data_encoded)),
            ('Divisive Hierarchical Clustering', DivisiveHierarchicalClustering()),
            ('BIRCH', BIRCHClustering()),
            ('Agglomerative BIRCH', AgglomerativeBirchClustering(self.data_encoded)),
            ('Spectral', SpectralClusteringModel(self.data_encoded)),
            ('OPTICS', OPTICSClustering(self.data_encoded)),
            ('Affinity Propagation', AffinityPropagationClustering(self.data_encoded)),
            ('Density Peaks', DensityPeaksClustering(self.data_encoded)),
            ('Self-Organizing Maps', SOMClustering(self.data_encoded))

        ]
        self.models = {i+1: model for i, model in enumerate(model_list)}

    def select_model(self):
        print("Choisissez un modèle de clustering:")
        for key, (name, _) in self.models.items():
            print(f"{key}: {name}")

        model_choice = int(input("Entrez le numéro correspondant au modèle:q "))
        if model_choice in self.models:
            return self.models[model_choice][1]
        else:
            print("Choix invalide.")
            return None

    def select_models(self):
        print("\n" + "="*80)
        print("Construction et évaluation des modèles de machine learning".center(80))
        print("="*80)
        print("Puisque vous avez un problème de clustering")
        print("Merci de sélectionner le(s) modèle(s) que vous voulez utiliser, séparés par des virgules (e.g., 1, 3, 5):")
        for idx, (name, _) in self.models.items():
            print(f"{idx}. {name}")
        selected_indices = input("Votre choix : ").split(',')
        selected_indices = [int(idx.strip()) for idx in selected_indices if idx.strip().isdigit() and int(idx.strip()) in self.models]
        return selected_indices

    def models_selected_clustering(self, model_indices):
        for idx in model_indices:
            model_name, model = self.models[idx]

            # Création dynamique du nom de la méthode
            method_name = f"run_{model_name.lower().replace('-', '_').replace(' ', '_')}_clustering"

            if hasattr(model, method_name):
                try:
                    print("\n" + "-"*80)
                    print(f"MÉTHODE : {model_name}".center(80))
                    print("-"*80)

                    getattr(model, method_name)()

                    # Analyse post-clustering avec les données originales
                    if hasattr(model, 'labels') and model.labels is not None:
                        analyzer = ClusterAnalyzer(self.data_original, model.labels)
                        analyzer.run_analysis()
                    else:
                        print("⚠️ Aucune étiquette de cluster disponible.")

                    print("\n" + "-"*80)
                    print(f"Processus '{model_name}' terminé avec succès.".center(80))
                    print("-"*80 + "\n")
                    print("="*80 + "\n")

                except Exception as e:
                    print(f"\n❌ Erreur avec le modèle '{model_name}': {type(e).__name__} - {e}")
                    print("Passage au modèle suivant...\n" + "-"*80)
            else:
                print(f"⚠️ Méthode '{method_name}' introuvable pour le modèle {model_name}")

    def run_selected_model(self):
        selected_indices = self.select_models()
        self.models_selected_clustering(selected_indices)
