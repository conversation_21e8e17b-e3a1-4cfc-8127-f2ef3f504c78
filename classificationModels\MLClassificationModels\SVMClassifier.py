import numpy as np
from sklearn.svm import SVC
from scipy.stats import uniform
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from evaluationModels.evaluation_classification import ClassifierEvaluator

class Method_SVM_Classifier:
    def __init__(self):
        self.best_model = None
        self.best_params = None
        self.best_score = None

    def train_svm(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation automatique des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'C': uniform(0.1, 10),
            'kernel': ['linear', 'rbf', 'poly'],
            'gamma': ['scale', 'auto'] + list(np.logspace(-3, 3, 7)),
            'degree': [2, 3, 4]
        }

        base_model = SVC(random_state=random_state)

        random_search = RandomizedSearchCV(
            estimator=base_model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(accuracy_score),
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)
        self.best_model = random_search.best_estimator_
        self.best_params = random_search.best_params_
        self.best_score = random_search.best_score_

        print(f" Paramètres optimaux : {self.best_params}")
        print(f" Score moyen en validation croisée : {self.best_score:.4f}")
        return self

    def predict(self, X_test):
        if self.best_model is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        return self.best_model.predict(X_test)

    def run_svm_classifier(self, X_train, y_train, X_test, y_test):
        self.train_svm(X_train, y_train)
        y_pred = self.predict(X_test)

        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
