from sklearn.ensemble import AdaBoostClassifier  
from sklearn.tree import DecisionTreeClassifier
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score 
from scipy.stats import randint, uniform

from evaluationModels.evaluation_classification import ClassifierEvaluator

# Définition d'une classe pour entraîner, prédire et évaluer un modèle AdaBoost
class Method_AdaBoost_Classifier:

    def __init__(self):
        # Attribut pour stocker le meilleur modèle entraîné
        self.best_model = None

    def train_adaboost(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        # Fonction d'entraînement avec recherche aléatoire d'hyperparamètres
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        # Définition des plages de valeurs à tester pour les hyperparamètres
        param_dist = {
            'n_estimators': randint(50, 300),       # Nombre d’estimateurs (arbres faibles)
            'learning_rate': uniform(0.01, 1.0)     # Taux d’apprentissage
        }

        # Définition du modèle de base : un arbre de décision simple (stump)
        base_learner = DecisionTreeClassifier(max_depth=1)

        # Initialisation du modèle AdaBoost avec le base learner
        model = AdaBoostClassifier(estimator=base_learner, random_state=random_state)

        # Mise en place de la recherche aléatoire avec validation croisée
        random_search = RandomizedSearchCV(
            estimator=model,                        # Modèle à optimiser
            param_distributions=param_dist,         # Dictionnaire des hyperparamètres
            n_iter=n_iter,                          # Nombre d’itérations d’essais
            scoring=make_scorer(accuracy_score),    # Fonction de scoring (exactitude)
            cv=cv,                                  # Nombre de folds pour la validation croisée
            random_state=random_state,              # Pour la reproductibilité
            n_jobs=-1                               # Utilise tous les cœurs CPU disponibles
        )

        # Entraînement du modèle avec RandomizedSearchCV
        random_search.fit(X_train, y_train)

        # Sauvegarde du meilleur modèle trouvé
        self.best_model = random_search.best_estimator_

        # Affichage des meilleurs hyperparamètres et du score
        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée : {random_search.best_score_:.4f}")
        return self

    def predict(self, X_test):
        # Fonction pour générer les prédictions sur les données test
        if self.best_model is None:
            raise ValueError("Le modèle n'a pas été entraîné.")  # Erreur si aucun modèle n’a été appris

        print("Prédiction avec le modèle optimal...")
        return self.best_model.predict(X_test)  # Prédiction à partir du meilleur modèle

    def run_adaboost_classifier(self, X_train, y_train, X_test, y_test):
        # Fonction principale pour tout exécuter : entraînement, prédiction, évaluation
        print("__________ Entraînement du modèle AdaBoost __________")
        self.train_adaboost(X_train, y_train)  # Étape d'entraînement

        y_pred = self.predict(X_test)          # Étape de prédiction

        print("__________ Évaluation du modèle AdaBoost __________")
        evaluator = ClassifierEvaluator(y_test, y_pred)  # Initialisation de l’évaluateur
        evaluator.evaluation_metrics()                   # Affichage des métriques d’évaluation
