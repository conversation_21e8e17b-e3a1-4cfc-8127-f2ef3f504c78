
from sklearn.model_selection import RandomizedSearchCV
from sklearn.neighbors import KNeighborsRegressor
from scipy.stats import randint as sp_randint
from evaluationModels.evaluation_regressor import RegressionEvaluator 

class Method_KNN_Regressor:
    def __init__(self):
        self.best_knn = None
    
    def train_knn(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        print("_________________Entraînement du modèle KNN pour la régression_________________")
        print("Veuillez patienter quelques instants...")

        knn = KNeighborsRegressor()
        param_dist = {
            'n_neighbors': sp_randint(1, 25),
            'metric': ['euclidean', 'manhattan', 'chebyshev', 'minkowski']
        }

        random_search = RandomizedSearchCV(knn, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)

        self.best_knn = random_search.best_estimator_
        print(f"Le modèle KNN de régression a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")

        return self

    def predict(self, X_test):
        if self.best_knn is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        else:
            print("La prédiction avec les données de test...")

        return self.best_knn.predict(X_test)

    def run_knn_regressor(self, X_train, y_train, X_test, y_test):
        # Entraînement du modèle
        self.train_knn(X_train, y_train)

        # Prédiction sur les données de test
        y_pred = self.predict(X_test)

        # Évaluation du modèle
        evaluator = RegressionEvaluator(y_test, y_pred)  # Assurez-vous que cette classe est définie ailleurs.
        evaluator.evaluation_metrics()

