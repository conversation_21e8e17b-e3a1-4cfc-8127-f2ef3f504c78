from lightgbm import LGBMClassifier
from scipy.stats import randint, uniform
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from evaluationModels.evaluation_classification import ClassifierEvaluator

import warnings
warnings.filterwarnings("ignore")

class Method_LightGBM_Classifier:
    def __init__(self):
        self.best_parameter = None

    def train_lgbm(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        param_dist = {
            'learning_rate': uniform(0.01, 0.2),
            'max_depth': randint(3, 8),
            'num_leaves': randint(2, 32),
            'min_data_in_leaf': randint(1, 10),
            'min_data_in_bin': randint(1, 10),
            'n_estimators': randint(50, 150)
        }

        # param_dist = {
        #     'num_leaves': randint(10, 100),
        #     'learning_rate': uniform(0.01, 0.3),
        #     'n_estimators': randint(50, 200),
        #     'max_depth': randint(3, 10)
        # }

        model = LGBMClassifier()

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(accuracy_score),
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)

        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_lightgbm_classifier(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle LightGBM______________")
        self.train_lgbm(X_train, y_train)
        y_pred = self.predict(X_test)
        print("_________________Évaluation du modèle_________________")
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
