from sklearn.metrics.pairwise import rbf_kernel, euclidean_distances
from sklearn.cluster import SpectralClustering
from sklearn.metrics import silhouette_score
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt

class SpectralClusteringModel:
    def __init__(self, data, max_clusters=10, gamma_range=None):
        self.data = data.values if isinstance(data, pd.DataFrame) else data
        self.max_clusters = max_clusters
        self.gamma_range = gamma_range if gamma_range else [0.1, 0.5, 1.0, 2.0, 5.0]
        self.best_k = None
        self.best_gamma = None
        self.model = None
        self.labels = None
        self.centroids = None

    def _find_optimal_k_gamma(self):
        best_score = -1
        best_config = (None, None)

        for gamma in self.gamma_range:
            affinity_matrix = rbf_kernel(self.data, gamma=gamma)
            for k in range(2, self.max_clusters + 1):
                model = SpectralClustering(n_clusters=k, affinity='precomputed', random_state=42, assign_labels='kmeans')
                labels = model.fit_predict(affinity_matrix)
                score = silhouette_score(self.data, labels)

                if score > best_score:
                    best_score = score
                    best_config = (k, gamma)

        self.best_k, self.best_gamma = best_config
        print(f"✅ Meilleur k : {self.best_k}, meilleur gamma : {self.best_gamma} | Silhouette : {best_score:.4f}")

    def _fit_model(self):
        if self.best_gamma is None or self.best_k is None:
            raise ValueError("Veuillez exécuter _find_optimal_k_gamma() avant d'appeler _fit_model().")
        
        affinity_matrix = rbf_kernel(self.data, gamma=self.best_gamma)
        self.model = SpectralClustering(n_clusters=self.best_k, affinity='precomputed', random_state=42, assign_labels='kmeans')
        self.labels = self.model.fit_predict(affinity_matrix)

        self.centroids = np.zeros((self.best_k, self.data.shape[1]))
        for i in range(self.best_k):
            mask = self.labels == i
            self.centroids[i] = np.mean(self.data[mask], axis=0)

    def _describe_clusters(self):
        print("\n📊 Description des clusters :")
        for label in range(self.best_k):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")
            
    def _visualize(self):
        if self.best_gamma is None:
            raise ValueError("Veuillez exécuter _find_optimal_k_gamma() avant d'appeler _visualize().")

        dims = self.data.shape[1]
        data = self.data
        data = self.data.values if isinstance(self.data, pd.DataFrame) else self.data
        labels = self.labels
        centroids = self.centroids

        # Afficher la matrice d'affinité
        plt.figure(figsize=(8, 6))
        affinity_matrix = rbf_kernel(self.data, gamma=self.best_gamma)
        plt.imshow(affinity_matrix, cmap='viridis')
        plt.colorbar(label='Affinité')
        plt.title("Matrice d'affinité")
        plt.show()

        if dims == 2:
            X1, X2 = data[:, 0], data[:, 1]
            distances = euclidean_distances(data, centroids)
            radius = [np.max(distances[labels == i, i]) for i in range(len(centroids))]

            plt.figure(figsize=(10, 8))
            plt.scatter(X1, X2, c=labels, s=40, cmap='viridis')
            plt.scatter(centroids[:, 0], centroids[:, 1], c='red', marker='x', s=100, label='Centroïdes')
            for i, centroid in enumerate(centroids):
                circle = plt.Circle(centroid, radius[i], color='black', fill=False)
                plt.gca().add_patch(circle)
            plt.title("Clustering Spectral (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.legend()
            plt.show()

        elif dims == 3:
            fig = plt.figure(figsize=(10, 8))
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(data[:, 0], data[:, 1], data[:, 2], c=labels, s=40, cmap='viridis')
            ax.scatter(centroids[:, 0], centroids[:, 1], centroids[:, 2], c='black', s=100, label='Centroïdes')
            ax.set_title("Clustering Spectral (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.legend()
            plt.show()
        else:
            print("Visualisation uniquement disponible pour 2D ou 3D.")

    def _plot_silhouette(self, scores):
        plt.figure(figsize=(10, 6))
        plt.plot(range(2, self.max_clusters + 1), scores, marker='o')
        plt.title("Scores de silhouette pour Spectral Clustering")
        plt.xlabel("Nombre de clusters (k)")
        plt.ylabel("Score de silhouette")
        plt.grid(True)
        plt.show()

    def run_spectral_clustering(self):
        self._find_optimal_k_gamma()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
