from sklearn.model_selection import RandomizedSearchCV
from sklearn.preprocessing import StandardScaler
from sklearn.neighbors import KNeighborsClassifier
from evaluationModels.evaluation_classification import ClassifierEvaluator
from scipy.stats import randint as sp_randint
from sklearn.metrics import make_scorer, accuracy_score 


class Method_KNN_Classifier:
    def __init__(self):
        # Attribut pour enregistrer le meilleur modèle entraîné
        self.best_parameter = None

    def train_knn(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        """
        Entraîne le modèle KNN avec RandomizedSearchCV
        X_train, y_train : jeu d'entraînement
        n_iter : nombre de combinaisons aléatoires à tester
        cv : nombre de plis pour validation croisée
        """
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        # Définition de la grille aléatoire des hyperparamètres
        param_dist = {
            'n_neighbors': sp_randint(1, 26),               # Choix aléatoire entre 1 et 25
            'metric': ['euclidean', 'manhattan',            # Distance utilisée
                       'chebyshev', 'minkowski']
        }

        # Définition du modèle de base
        model = KNeighborsClassifier()

        # Création de l'objet RandomizedSearchCV
        random_search = RandomizedSearchCV(
            estimator=model,                      # Modèle à optimiser
            param_distributions=param_dist,       # Dictionnaire des hyperparamètres à explorer
            n_iter=n_iter,                         # Nombre d’essais aléatoires
            scoring=make_scorer(accuracy_score),  # Fonction de scoring
            cv=cv,                                 # Validation croisée à k plis (ici 5)
            random_state=random_state,             # Pour reproductibilité
            n_jobs=-1                              # Utilisation de tous les cœurs pour aller plus vite
        )

        # Entraînement du RandomizedSearchCV sur le jeu d'entraînement
        random_search.fit(X_train, y_train)

        # Sauvegarde du meilleur modèle trouvé
        self.best_parameter = random_search.best_estimator_

        # Affichage des meilleurs hyperparamètres
        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        """
        Prédit les classes avec le meilleur modèle entraîné
        """
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")  # Protection si on oublie d’entraîner
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_knn_classifier(self, X_train, y_train, X_test, y_test):
        """
        Fonction complète : entraînement + prédiction + évaluation
        """
        print("______________Entraînement du modèle KNN______________")
        
        # Entraînement du modèle avec sélection automatique des meilleurs hyperparamètres
        self.train_knn(X_train, y_train)

        # Prédiction sur le jeu de test
        y_pred = self.predict(X_test)

        # Évaluation des performances
        print('_________________Évaluation du modèle_________________')
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()  # Affiche les métriques (exactitude, précision, rappel, etc.)
