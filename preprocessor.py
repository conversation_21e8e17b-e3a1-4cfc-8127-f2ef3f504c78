import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler, StandardScaler, OneHotEncoder, LabelEncoder

from sklearn.model_selection import train_test_split
import re
import category_encoders as ce
from sklearn.model_selection import train_test_split


class DataPreprocessor:
    def __init__(self, data):
        if not isinstance(data, pd.DataFrame):
            raise ValueError("Les données fournies ne sont pas un DataFrame.")
        self.data = data

    def infer_frequency(self, date_series):
        """
        Déduit la fréquence la plus courante dans une série de dates.
        """
        deltas = date_series.diff().dropna().value_counts()
        most_common_delta = deltas.idxmax()
        
        # Inférer la fréquence basée sur la différence la plus commune
        if most_common_delta == pd.Timedelta(days=1):
            return 'D'
        elif most_common_delta == pd.Timedelta(weeks=1):
            return 'W'
        elif most_common_delta in [pd.<PERSON>del<PERSON>(days=28), pd.<PERSON><PERSON><PERSON>(days=30), pd.Timedelta(days=31)]:
            # Vérifier si la première date est le début du mois
            if date_series.dt.is_month_start.all():
                return 'MS'
            else:
                return 'M'
        elif most_common_delta in [pd.Timedelta(days=90), pd.Timedelta(days=91), pd.Timedelta(days=92), pd.Timedelta(days=93)]:
            # Vérifier si la première date est le début du trimestre
            if date_series.dt.is_quarter_start.all():
                return 'QS'
            else:
                return 'Q'
        elif most_common_delta in [pd.Timedelta(days=365), pd.Timedelta(days=366)]:
            # Vérifier si la première date est le début de l'année
            if date_series.dt.is_year_start.all():
                return 'AS'
            else:
                return 'A'
        else:
            # Si aucune fréquence spécifique n'est trouvée, retourner None
            return None

    def fill_missing_dates(self, df, date_col, fill_value=0, cat_fill_value='Non applicable'):
        """
        Remplit les dates manquantes dans un DataFrame et remplace les lignes manquantes par des valeurs spécifiées.
        """
        df[date_col] = pd.to_datetime(df[date_col])

        # Déterminer les dates de début et de fin à partir des données existantes
        start_date = df[date_col].min()
        end_date = df[date_col].max()

        # Inférer la fréquence des dates
        freq = self.infer_frequency(df[date_col])

        # Créer une plage de dates complète en utilisant la fréquence déduite
        all_dates = pd.date_range(start=start_date, end=end_date, freq=freq)

        # Créer un DataFrame avec la plage de dates complète
        df_all_dates = pd.DataFrame(all_dates, columns=[date_col])

        # Fusionner avec le DataFrame original pour identifier les dates manquantes
        df_merged = df_all_dates.merge(df, on=date_col, how='left')

        # Identifier les colonnes catégorielles et numériques
        categorical_cols = [col for col in df.columns if df[col].dtype == 'object' and col != date_col]
        numerical_cols = [col for col in df.columns if df[col].dtype != 'object' and col != date_col]

        # Remplacer les valeurs NaN par les valeurs de remplissage spécifiées
        df_merged.fillna({col: fill_value for col in numerical_cols}, inplace=True)
        df_merged.fillna({col: cat_fill_value for col in categorical_cols}, inplace=True)

        # Conserver les types de données d'origine
        for col in df.columns:
            if col != date_col: 
                df_merged[col] = df_merged[col].astype(df[col].dtype)

        print(f"Les dates manquantes ont été comblées dans la colonne '{date_col}'. Les colonnes catégorielles ont été remplies avec '{cat_fill_value}'.")

        return df_merged
    
    def prepare_time_series_data(self):

        datetime_columns = [col for col in self.data.columns if pd.api.types.is_datetime64_any_dtype(self.data[col])]
        
        print(f"Colonnes de type datetime détectées : {datetime_columns}")

        # Afficher les colonnes disponibles et demander à l'utilisateur de choisir une colonne de date
        for i, col in enumerate(datetime_columns):
            print(f"{i+1}: {col}")
        index_col = int(input("Veuillez entrer le numéro de la colonne de date à traiter : ")) - 1

        if index_col < 0 or index_col >= len(datetime_columns):
            print("Index hors de portée.")
            return None

        chosen_col = datetime_columns[index_col]

        # Trier les données par la colonne de date sélectionnée
        self.data = self.data.sort_values(by=chosen_col).reset_index(drop=True)
        print(f"Les données ont été triées par la colonne '{chosen_col}' en ordre chronologique.")
        print(self.data)

        # Demander si l'utilisateur souhaite séparer la date en composants
        separate_date = input(
            "Voulez-vous séparer la date en mois, jour et année ?\n"
            "1. Oui\n"
            "2. Non\n"
            "Veuillez entrer votre choix (1-2) : "
        ).strip()

        if separate_date == '1':
            # Ajouter des colonnes pour année, mois, jour, etc.
            self.data['Year'] = self.data[chosen_col].dt.year
            self.data['Month'] = self.data[chosen_col].dt.month
            self.data['Day'] = self.data[chosen_col].dt.day
            self.data['Hour'] = self.data[chosen_col].dt.hour
            self.data['Minute'] = self.data[chosen_col].dt.minute
            self.data['Second'] = self.data[chosen_col].dt.second
            print("La date a été séparée en colonnes 'Year', 'Month', 'Day', 'Hour', 'Minute' et 'Second'.")
        else:
            print("La colonne de date a été conservée telle quelle.")

    def convert_date_columns(self):
        # Vérifiez chaque colonne pour voir si elle contient des dates
        date_patterns = [
            re.compile(r'^\d{4}-\d{2}-\d{2}$'),  # YYYY-MM-DD
            re.compile(r'^\d{4}/\d{2}/\d{2}$'),  # YYYY/MM/DD
            re.compile(r'^\d{2}/\d{2}/\d{4}$'),  # DD/MM/YYYY
            re.compile(r'^\d{4}-\d{2}$')  # YYYY-MM
        ]

        for col in self.data.columns:
            try:
                if self.data[col].apply(lambda x: any(pat.match(str(x)) for pat in date_patterns)).all():
                    self.data[col] = pd.to_datetime(self.data[col], errors='coerce', format='%Y-%m')
                    print(f"La colonne '{col}' a été convertie en dates.")
            except Exception as e:
                print(f"Erreur lors de la conversion de la colonne '{col}' en dates : {e}")

    def ask_change_dtype(self):
        
        response = input(
                    "\n▶ Voulez-vous changer les types de données des colonnes ?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()
        while response not in ['1', '2']:  # S'assure que la réponse est valide
            print("Réponse invalide. Veuillez répondre 'oui' ou 'non'.")
            response = input(
                    "\n▶ Voulez-vous changer les types de données des colonnes ?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()

        if response == '1':
            print("Colonnes disponibles:")
            for i, col in enumerate(self.data.columns):
                print(f"{i+1}: {col}")

            while True:
                cols_to_change = input("Entrez les numéros des colonnes à modifier, séparés par une virgule (ex: 1,3): ")
                try:
                    col_indices = [int(x.strip()) - 1 for x in cols_to_change.split(',') if x.strip().isdigit()]
                    if not col_indices or not all(0 <= idx < len(self.data.columns) for idx in col_indices):
                        raise ValueError("Certains indices sont hors de portée ou invalides.")
                    break
                except ValueError as e:
                    print(f"Erreur: {str(e)}. Veuillez entrer uniquement des numéros de colonnes valides.")

            data_types = {
                '1': 'int',
                '2': 'float',
                '3': 'object',
                '4': 'bool',
                '5': 'category',
                '6': 'datetime64[ns]'
            }

            print("\nTypes de données disponibles:")
            for key, value in data_types.items():
                print(f"{key}: {value}")

            for index in col_indices:
                col_name = self.data.columns[index]
                current_dtype = self.data[col_name].dtype
                while True:
                    print(f"\nColonne: {col_name}, Type actuel: {current_dtype}")
                    type_choice = input("Entrez le numéro du nouveau type de données (laissez vide pour conserver le type actuel): ").strip()
                    if not type_choice:  # Laisser le type actuel si l'entrée est vide
                        break
                    new_type = data_types.get(type_choice)
                    if new_type:
                        try:
                            self.data[col_name] = self.data[col_name].astype(new_type)
                            print(f"Type de la colonne {col_name} changé en {new_type}.")
                            break
                        except Exception as e:
                            print(f"Erreur lors du changement de type pour la colonne {col_name}: {e}")
                    else:
                        print("Erreur: Choix de type de données invalide.")

        return self.data

    def get_categorical_columns(self):
            # Exclure les colonnes de date et les colonnes numériques
            date_columns = [col for col in self.data.columns if self.data[col].dtype == 'datetime64[ns]']
            numeric_columns = self.data.select_dtypes(include=[np.number]).columns
            categorical_columns = [col for col in self.data.select_dtypes(exclude=[np.number]).columns if col not in date_columns]
            return categorical_columns
    
    def determine_target(self):
        while True:
            print("\nMerci de sélectionner le numéro correspondant à la variable de sortie à partir de cette liste :")
            for idx, column in enumerate(self.data.columns):
                print(f"{idx}. {column}")

            try:
                
                target_index = int(input("Entrez le numéro de la variable de sortie : "))
                if 0 <= target_index < len(self.data.columns):
                    self.target = self.data.columns[target_index]
                    print(f"La variable de sortie sélectionnée est '{self.target}'.")
                    target_type = self.data[self.target].dtype

                    # Déterminer le type de problème
                    if target_type == 'object' or pd.api.types.is_categorical_dtype(self.data[self.target]):
                        print(f"'{self.target}' est une variable catégorielle. Nous avons un problème de classification.")
                        return self.target
                    elif pd.api.types.is_numeric_dtype(self.data[self.target]):
                        print(f"'{self.target}' est une variable quantitative. Nous avons un problème de régression.")
                        return self.target
                    else:
                        print(f"Erreur: La variable de sortie '{self.target}' a un type inconnu. Merci de réessayer.")
                else:
                    print("Erreur : Index hors des limites. Merci de sélectionner un index valide.")
            except ValueError:
                print("Erreur : Veuillez entrer un numéro valide.")

    def display_duplicate_rows(self):
        # Utilisation de 'duplicated' pour identifier les duplicatas, en marquant toutes les occurrences à l'exception de la première
        duplicates = self.data.duplicated(keep='first')
        # Comptage des duplicatas
        num_duplicates = duplicates.sum()
        # Affichage du nombre de lignes dupliquées
        if num_duplicates > 0:
            print(f"Nombre de lignes dupliquées: {num_duplicates}")
        else:
            print("Il n'y a pas de lignes dupliquées.")   

    def remove_duplicates(self):
        
        duplicated_rows = self.data.duplicated().sum()
        duplicated_columns = 0
        
        # Vérifier les doublons de colonnes
        for i, col in enumerate(self.data.columns):
            for j in range(i+1, len(self.data.columns)):
                if self.data[col].equals(self.data.iloc[:, j]):
                    duplicated_columns += 1
                    print(f"Colonnes dupliquées: {col} et {self.data.columns[j]}")

        if duplicated_columns > 0:
            choice = input(
                "Voulez-vous supprimer les doublons de colonnes?\n"
                "1. Ne rien faire\n"
                "2. Supprimer les colonnes dupliquées\n"
                "Veuillez entrer votre choix (1-2) : "
            ).strip()

            if choice == "1":  # Supprimer les colonnes dupliquées

                #self.data.drop(columns=duplicated_columns, inplace=True)
                self.data.drop_duplicates(inplace=True)
                print("✔ Colonnes dupliquées supprimées")

        if duplicated_rows > 0:
            while True: #parie modifiée par l'équipe test
                choice = input(
                    "Voulez-vous supprimer les doublons de lignes?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()

                if choice in {"1", "2"}:
                    break
                else:
                    print("Entrée invalide. Veuillez entrer '1' pour Oui ou '2' pour Non.")


            if choice == "1":  # Supprimer les lignes dupliquées
                self.data.drop_duplicates(inplace=True)
                print("✔ Lignes dupliquées supprimées")

    def display_missing_values(self):
        total_rows = len(self.data)
        print("Pourcentage de valeurs manquantes par colonne:")
        for column in self.data.columns:
            missing_count = self.data[column].isnull().sum()
            if missing_count > 0:
                missing_percentage = (missing_count / total_rows) * 100
                print(f"{column}: {missing_percentage:.2f}%")
            else:
                print(f"{column}: 0.00%")
                
    def choose_handling_method(self, data_type):
        options = {
            "numeric": "\n1. Supprimer les lignes\n2. Remplacer par la moyenne\n3. Remplacer par la médiane\n4. Remplacer par le mode\n5. Remplacer par le minimum\n6. Remplacer par le maximum",
            "categorical": "\n1. Supprimer les lignes\n2. Remplacer par le mode"
        }
        while True:
            choice = input(
                f"\n▶  Comment voulez-vous gérer les valeurs manquantes pour les données {data_type}?\n" +
                options[data_type] +
                "\nVeuillez entrer votre choix : "
            ).strip()
            valid_choices = ["1", "2", "3", "4", "5", "6"] if data_type == "numeric" else ["1", "2"]
            if choice in valid_choices:
                print("✔ Les valeurs manquantes ont été traitées.")
                return int(choice)
            else:
                print("Choix invalide. Réessayez.")

    def handle_missing_values(self):
        numeric_columns = self.data.select_dtypes(include=[np.number]).columns
        non_numeric_columns = self.data.select_dtypes(exclude=[np.number]).columns

        # Vérifier s'il y a des valeurs manquantes dans les colonnes numériques
        if self.data[numeric_columns].isnull().sum().sum() > 0:
            print("\n▶ Traitement des valeurs manquantes pour les données numériques:")
            numeric_choice = self.choose_handling_method("numeric")
            self.apply_missing_value_strategy(numeric_columns, numeric_choice)

        # Vérifier s'il y a des valeurs manquantes dans les colonnes non numériques
        if self.data[non_numeric_columns].isnull().sum().sum() > 0:
            print("\n▶ Traitement des valeurs manquantes pour les données catégorielles:")
            categorical_choice = self.choose_handling_method("categorical")
            self.apply_missing_value_strategy(non_numeric_columns, categorical_choice)

    def apply_missing_value_strategy(self, columns, choice):
        
        if choice == 1:
            self.data.dropna(subset=columns, inplace=True)
        elif choice == 2 and self.data[columns].dtypes.iloc[0] == np.number:
            self.data[columns] = self.data[columns].fillna(self.data[columns].mean())
        elif choice == 3 and self.data[columns].dtypes[0] == np.number:
            self.data[columns] = self.data[columns].fillna(self.data[columns].median())
        elif choice == 2 and self.data[columns].dtypes.iloc[0] != np.number:
            self.data[columns] = self.data[columns].fillna(self.data[columns].mode().iloc[0])
        elif choice == 3 and self.data[columns].dtypes.iloc[0] != np.number:
            self.data[columns] = self.data[columns].fillna("Unknown")
        elif choice in [4, 5, 6]:  # Mode, Min, Max uniquement pour numériques
            if choice == 4:
                self.data[columns] = self.data[columns].fillna(self.data[columns].mode().iloc[0])
            elif choice == 5:
                self.data[columns] = self.data[columns].fillna(self.data[columns].min())
            elif choice == 6:
                self.data[columns] = self.data[columns].fillna(self.data[columns].max())

    # Demander à l'utilisateur s'il souhaite appliquer une normalisation sur les colonnes numériques
    def normalize_numeric_columns(self):
        choice = input(
             "\n▶  Souhaitez-vous appliquer la normalisation des données ?\n"
              "1. Oui\n"
               "2. Non\n"
                "Veuillez entrer votre choix (1-2) : "
                 ).strip()
        if choice == '2':
             print("Normalisation sautée.")
             return self.data
        
        elif choice == '1':
             while True:
                   print("\nOptions de normalisation des caractéristiques :")
                   print("1. Normalisation Min-Max (redimensionne les données entre 0 et 1)")
                   print("2. Standardisation Z-score (moyenne = 0, écart-type = 1)")
                   norm_choice = input("Entrez votre choix (1 ou 2) : ").strip()

                   if norm_choice == '1':
                       scaler = MinMaxScaler()
                       for col in self.data.select_dtypes(include=[np.number]).columns:
                           self.data[col] = scaler.fit_transform(self.data[col].values.reshape(-1, 1))
                       print("✔ Normalisation Min-Max appliquée avec succès.")
                       break
                   elif norm_choice == '2':
                        scaler = StandardScaler()
                        for col in self.data.select_dtypes(include=[np.number]).columns:
                            self.data[col] = scaler.fit_transform(self.data[col].values.reshape(-1, 1))
                        print("✔ Standardisation Z-score appliquée avec succès.")
                        break
                   else:
                      print("Choix invalide. Veuillez entrer '1' pour la Normalisation Min-Max ou '2' pour la Standardisation Z-score.")
        else:
            print("Choix invalide. Veuillez entrer '1' pour Oui ou '2' pour Non.")
    
        return self.data
        # we mean by terget reference the column used in target encoding , and we mean by variable_cible the output one

   # we mean by terget reference the column used in target encoding , and we mean by variable_cible the output one
    def encode_categorical_columns(self, variable_cible=None):  

        # Identifier les colonnes catégorielles dans le dataset
        categorical_columns = self.data.select_dtypes(include=['object']).columns.tolist()

        if len(categorical_columns) == 0:
            print("Il n'y a pas de colonnes catégorielles dans le dataset.")
            return self.data, [variable_cible]

        # Afficher les données avant transformation
        print("Données avant encodage :")
        print(self.data.head())

        # Demander une seule fois le choix d'encodage pour toutes les colonnes catégorielles
        choice = int(input(
            "\nOptions d'encodage pour toutes les colonnes catégorielles :\n"
            "1. Encodage Label (entier)\n"
            "2. Encodage One-Hot\n"
            "3. Target Encoding\n"
            "4. Aucune transformation\n"
            "Veuillez entrer votre choix (1-4) : "
        ).strip())

        new_target_columns = [variable_cible]  # Initialement, la variable cible reste inchangée

        if choice == 2:  # Appliquer l'encodage One-Hot
            for col in categorical_columns:
                # Appliquer One-Hot Encoding
                self.data = pd.get_dummies(self.data, columns=[col], prefix=col)
                print(f"Encodage One-Hot appliqué pour colonne : {col}")

            # Si la variable cible est catégorielle, récupérer les nouvelles colonnes liées
            if variable_cible in categorical_columns:
                new_target_columns = [col for col in self.data.columns if col.startswith(f"{variable_cible}_")]
            return new_target_columns

        elif choice == 1:  # Encodage Label (entier)
            for col in categorical_columns:
                encoder = LabelEncoder()
                self.data[col] = self.data[col].fillna('Missing')  # Remplacer les valeurs manquantes
                self.data[col] = encoder.fit_transform(self.data[col])
            print("Encodage Label appliqué à toutes les colonnes catégorielles.")
            return variable_cible

        elif choice == 3:  # Target Encoding
            while True:
                target_reference = input("Veuillez entrer le nom de la colonne cible pour le Target Encoding : ")
                if target_reference not in self.data.columns:
                    print(f"Erreur : La colonne '{target_reference}' n'existe pas dans le dataset.")
                    continue
                target_type = self.data[target_reference].dtype

                if target_type == 'object' or pd.api.types.is_categorical_dtype(self.data[target_reference]):
                    print("La cible ne doit pas être catégorielle. Veuillez entrer une colonne valide.")
                else:
                    break
                
            encoder = ce.TargetEncoder(cols=categorical_columns)
            self.data[categorical_columns] = encoder.fit_transform(self.data[categorical_columns], self.data[target_reference])
            print(f"Target Encoding appliqué à toutes les colonnes catégorielles en utilisant la colonne cible '{target_reference}'.")
            return variable_cible

        elif choice == 4:  # Aucune transformation
            print("Aucune transformation appliquée aux colonnes catégorielles.")
        else:
            print(f"Choix invalide ({choice}). Aucune transformation n'a été effectuée.")

        # Afficher les données après transformation
        print("\nDonnées après encodage :")
        print(self.data.head())

        # Retourner le DataFrame transformé et les nouvelles variables cibles
        return variable_cible

    def select_features_to_keep(self, target):
        while True:
            # Demander si conserver toutes les variables
            choice = input("\nVoulez-vous conserver toutes les variables ?\n 1. Oui\n 2. Non\n"
                           "Veuillez entrer votre choix (1-2) :").strip()

            if choice == '1':
                # print("Toutes les variables ont été conservées, sauf la variable cible.")
                print("Toutes les variables ont été conservées.")

                return self.data.drop(columns=target)

            elif choice == '2':
                break
            else:
                print("Choix invalide. Veuillez entrer '1' pour conserver toutes les variables ou '2' pour en éliminer.")
        
        # Filtrer les caractéristiques pour exclure la variable cible
        features = [col for col in self.data.columns if col != target]
        print("Caractéristiques disponibles:")
        for idx, feature in enumerate(features):
            print(f"{idx + 1}. {feature}")
        
        selected_indices = input("Entrez les numéros des caractéristiques à supprimer, séparés par une virgule (ex: 1, 3, 5) : ")
        selected_indices = [int(x.strip()) - 1 for x in selected_indices.split(',') if x.strip().isdigit()]

        # Filtrer pour s'assurer que les indices sont valides
        selected_indices = [index for index in selected_indices if index < len(features) and index >= 0]

        features_to_remove = [features[index] for index in selected_indices]
        print("Caractéristiques sélectionnées pour être supprimées : ", features_to_remove)        
        
        return self.data.drop(columns=features_to_remove)
    
    def select_features_to_keep_for_clustering(self):
        while True:
            # Demander si conserver toutes les variables
            choice = input("Voulez-vous conserver toutes les caractéristiques ? 1. Oui 2. Non : ").strip()

            if choice == '1':
                print("Toutes les caractéristiques ont été conservées.")
                return self.data

            elif choice == '2':
                break
            else:
                print("Choix invalide. Veuillez entrer '1' pour conserver toutes les caractéristiques ou '2' pour en éliminer.")
        
        # Afficher les caractéristiques disponibles
        features = self.data.columns
        print("Caractéristiques disponibles:")
        for idx, feature in enumerate(features):
            print(f"{idx + 1}. {feature}")
        
        # Demander à l'utilisateur de sélectionner les caractéristiques à supprimer
        selected_indices = input("Entrez les numéros des caractéristiques à supprimer, séparés par une virgule (ex: 1, 3, 5) : ")
        selected_indices = [int(x.strip()) - 1 for x in selected_indices.split(',') if x.strip().isdigit()]

        # Filtrer pour s'assurer que les indices sont valides
        selected_indices = [index for index in selected_indices if index < len(features) and index >= 0]

        # Récupérer les noms des caractéristiques à supprimer
        features_to_remove = [features[index] for index in selected_indices]
        print("Caractéristiques sélectionnées pour être supprimées : ", features_to_remove)

        # Supprimer les caractéristiques sélectionnées
        return self.data.drop(columns=features_to_remove)

    def split_data(self, X, y, default_test_size=0.2, random_state=42):
        # Demander à l'utilisateur d'entrer la taille de l'ensemble de test
        while True:
                test_size_input = input(f"\n▶ Veuillez entrer la taille de l'ensemble de test (0.0 < taille < 1.0) : ").strip()
                if test_size_input == '':
                    test_size = default_test_size
                    break
                else:
                    try:
                        test_size = float(test_size_input)
                        if 0.0 < test_size < 1.0:
                            if test_size > 0.5:
                                # Demander confirmation si la taille de test est supérieure à 0.5
                                confirmation = input("Vous avez choisi un ensemble de test plus grand que l'ensemble d'entraînement. Êtes-vous sûr ? (oui/non) : ").strip().lower()
                                if confirmation == 'oui':
                                    break
                                else:
                                    print("Veuillez choisir une taille de test inférieure à 0.5.")
                                    continue
                            break
                        else:
                            print("Erreur: La taille doit être entre 0.0 et 1.0, exclusive. Veuillez entrer une valeur valide.")
                    except ValueError:
                        print("Entrée invalide. Veuillez entrer un nombre décimal entre 0.0 et 1.0.")

        # Diviser les données en ensembles d'entraînement et de test
        print("Les données ont été divisées en ensembles d'entraînement et de test avec :")
        X_train, X_test, y_train, y_test = train_test_split(X, y, test_size=test_size, random_state=random_state)
        print("\tX_train shape:", X_train.shape)
        print("\tX_test shape:", X_test.shape)
        print("\ty_train shape:", y_train.shape)
        print("\ty_test shape:", y_test.shape)

        print("\n" + "-"*80)
        print("✔ Prétraitement terminé".center(80))
        print("-"*80)

        return X_train, X_test, y_train, y_test


    def split_sequential_data(self, data, target, sequence_length=10, default_test_size=0.2, random_state=42):
        """
        Divise les données séquentielles en ensembles d'entraînement et de test, tout en créant des séquences temporelles.
        """
        
        # Déterminer la longueur de la séquence par défaut basée sur la taille des données
        def suggest_sequence_length(data, min_seq_length=5, max_seq_length=50):
            # Utiliser la taille des données pour proposer une longueur de séquence
            data_length = len(data)
            
            # Proposer une longueur de séquence entre min_seq_length et max_seq_length, dépendant de la taille des données
            if data_length > 1000:
                suggested_length = max_seq_length  # Plus de données = séquences plus longues
            elif data_length > 500:
                suggested_length = 30  # Moyenne
            else:
                suggested_length = min_seq_length  # Moins de données = séquences plus courtes
            
            return suggested_length

        # Suggérer la longueur de la séquence
        suggested_seq_length = suggest_sequence_length(data)

        # Demander à l'utilisateur s'il souhaite changer la longueur de la séquence
        print(f"Longueur de séquence suggérée : {suggested_seq_length}")
        user_input = input(f"Voulez-vous changer cette longueur de séquence (oui/non) ?\n" "1. Oui\n" "2. Non\n" 
            "Veuillez entrer votre choix (1-2) : ").strip().lower()
        if user_input == '1':
            while True:
                try:
                    new_seq_length = int(input("Veuillez entrer la nouvelle longueur de la séquence : "))
                    if new_seq_length > 0:
                        sequence_length = new_seq_length
                        break
                    else:
                        print("La longueur de la séquence doit être un nombre positif.")
                except ValueError:
                    print("Entrée invalide. Veuillez entrer un nombre entier.")

        else :
            sequence_length = suggested_seq_length
        # Demander la taille de l'ensemble de test
        while True:
            test_size_input = input(f"\n▶ Veuillez entrer la taille de l'ensemble de test (0.0 < taille < 1.0) : ").strip()
            if test_size_input == '':
                test_size = default_test_size
                break
            else:
                try:
                    test_size = float(test_size_input)
                    if 0.0 < test_size < 1.0:
                        if test_size > 0.5:
                            confirmation = input("Vous avez choisi un ensemble de test plus grand que l'ensemble d'entraînement. Êtes-vous sûr ? (oui/non) : ").strip().lower()
                            if confirmation == 'oui':
                                break
                            else:
                                print("Veuillez choisir une taille de test inférieure à 0.5.")
                                continue
                        break
                    else:
                        print("Erreur: La taille doit être entre 0.0 et 1.0, exclusive. Veuillez entrer une valeur valide.")
                except ValueError:
                    print("Entrée invalide. Veuillez entrer un nombre décimal entre 0.0 et 1.0.")
        
        # Calculer la taille du test
        test_size_absolute = int(len(data) * test_size)
        
        # Diviser les données
        train_data = data[:-test_size_absolute]
        test_data = data[-test_size_absolute:]
        train_target = target[:-test_size_absolute]
        test_target = target[-test_size_absolute:]

        # Créer des séquences pour l'entraînement et le test
        def create_sequences(data, target, seq_length):
            X, y = [], []
            max_index = len(data) - seq_length  # Dernier index valide
            for i in range(max_index):
                X.append(data[i:i + seq_length])
                y.append(target.iloc[i + seq_length])  # Utilisation de .iloc pour un accès sûr
            return np.array(X), np.array(y)

        # Passer la longueur de séquence mise à jour à create_sequences
        X_train_seq, y_train_seq = create_sequences(train_data, train_target, sequence_length)
        X_test_seq, y_test_seq = create_sequences(test_data, test_target, sequence_length)

        # Afficher les résultats
        print("Les données séquentielles ont été divisées en ensembles d'entraînement et de test avec :")
        print("\tX_train_seq shape:", X_train_seq.shape)
        print("\tX_test_seq shape:", X_test_seq.shape)
        print("\ty_train_seq shape:", y_train_seq.shape)
        print("\ty_test_seq shape:", y_test_seq.shape)

        return X_train_seq, X_test_seq, y_train_seq, y_test_seq

    def preprocess(self):
        print("\n" + "="*80)
        print("Prétraitement des données".center(80))
        print("="*80)
        self.data.columns = [re.sub(r'[^a-zA-Z0-9]+', '', col) for col in self.data.columns]

        print("\n▶ Vérification des doublons...")
        self.display_duplicate_rows()
        self.remove_duplicates()

        print("\n▶ Vérification des valeurs manquantes...")
        self.display_missing_values()
        self.handle_missing_values()        
        return self.data


        # print("\n▶ Nettoyage des noms de colonnes...")
        # self.data.columns = [re.sub(r'[^a-zA-Z0-9]+', '', col) for col in self.data.columns]
        # print("→ Caractères spéciaux supprimés.")
        print("\n✔ Prétraitement terminé.")