import pandas as pd
import numpy as np
from sklearn.feature_selection import mutual_info_classif, mutual_info_regression, chi2
from scipy.stats import f_oneway, kruskal, spearmanr, shapiro, levene
from sklearn.preprocessing import Label<PERSON><PERSON><PERSON>, MinMaxScaler

class FeatureSelector:
    def __init__(self, X, y):
        """Initialisation du sélecteur de caractéristiques avec les données X et y."""
        if not isinstance(X, pd.DataFrame):
            raise ValueError("Les features X doivent être dans un DataFrame pandas.")
        if not isinstance(y, pd.Series):
            raise ValueError("Le target y doit être une Series pandas contenant une seule colonne.")

        self.X = X
        self.y = y
        self.results = {}
        self.removed_features = []
        self.selected_features = []

    def _check_variable_types(self):
        """Vérifie les types des variables X et y (numérique ou catégoriel)."""
        X_types = {
            col: 'catégorielle' if pd.api.types.is_object_dtype(self.X[col]) or pd.api.types.is_categorical_dtype(self.X[col]) else 'numérique'
            for col in self.X.columns
        }
        y_type = 'catégorielle' if pd.api.types.is_object_dtype(self.y) or pd.api.types.is_categorical_dtype(self.y) else 'numérique'
        return X_types, y_type

    def _apply_chi2_test(self):
        """Applique le test de Chi-square pour toutes les variables catégorielles."""
        X_encoded = self.X.apply(LabelEncoder().fit_transform)
        y_encoded = LabelEncoder().fit_transform(self.y)
        chi2_scores, p_values = chi2(X_encoded, y_encoded)
        results = {col: {'méthode': 'Chi-square', 'score': score, 'pvalue': p_value} 
                   for col, score, p_value in zip(self.X.columns, chi2_scores, p_values)}
        return results

    def _apply_mutual_info(self, is_categorical_y):
        """Calcule les scores d'information mutuelle après encodage et normalisation, 
    affiche les scores triés et permet à l'utilisateur de choisir les k meilleures features."""

        # Copier X pour éviter de modifier les données d'origine
        X_encoded = self.X.copy()

        # Encoder les variables catégorielles
        for col in X_encoded.select_dtypes(include=['object', 'category']).columns:
            encoder = LabelEncoder()
            X_encoded[col] = encoder.fit_transform(X_encoded[col])

        # Normaliser les variables numériques (Min-Max Scaling entre 0 et 1)
        scaler = MinMaxScaler()
        X_encoded[X_encoded.select_dtypes(include=['int64', 'float64']).columns] = scaler.fit_transform(
             X_encoded[X_encoded.select_dtypes(include=['int64', 'float64']).columns]
        )

        # Calculer les scores d'Information Mutuelle
        mi_scores = mutual_info_classif(X_encoded, self.y) if is_categorical_y else mutual_info_regression(X_encoded, self.y)
 
        # Trier les features par Information Mutuelle (ordre décroissant)
        mi_results = sorted(zip(self.X.columns, mi_scores), key=lambda x: x[1], reverse=True)

        # Afficher les résultats triés
        print("\n **Scores d'Information Mutuelle (triés) :**")
        for rank, (col, score) in enumerate(mi_results, 1):
            print(f"{rank}. {col}: {score:.4f}")

        # Demander à l'utilisateur combien de features il souhaite garder
        while True:
            try:
                k = int(input(f"\n Combien de features souhaitez-vous garder parmi {len(self.X.columns)} ? (Entrez un nombre) : ").strip())
                if 1 <= k <= len(self.X.columns):
                    break
                print(f"Veuillez entrer un nombre valide entre 1 et {len(self.X.columns)}")
            except ValueError:
                print("Entrée invalide. Veuillez entrer un nombre entier.")

        # Garder uniquement les k meilleures features
        selected_features = [col for col, _ in mi_results[:k]]

        # Retourner un dictionnaire avec uniquement les features sélectionnées
        return {col: {'méthode': 'Information Mutuelle', 'score': score} for col, score in mi_results if col in selected_features}

    
    def _apply_spearman_correlation(self, col):
        """Applique la corrélation de Spearman après encodage éventuel."""
        if self.X[col].dtype == 'object' or pd.api.types.is_categorical_dtype(self.X[col]):
            encoder = LabelEncoder()
            X_encoded = encoder.fit_transform(self.X[col])
        else:
            X_encoded = self.X[col]
        
        stat, pvalue = spearmanr(X_encoded, self.y)
        return stat, pvalue

    def _apply_anova_or_kruskal(self, col):
        """Applique l'ANOVA ou le test de Kruskal-Wallis selon les conditions."""
        if not np.issubdtype(self.y.dtype, np.number):
            encoder = LabelEncoder()
            y_encoded = encoder.fit_transform(self.y)
        else:
            y_encoded = self.y

        groups = [self.X[col][y_encoded == level] for level in np.unique(y_encoded)]
        normality = all((len(group) > 3 and np.ptp(group) > 0 and shapiro(group).pvalue > 0.05) for group in groups)
        homogeneity = levene(*groups).pvalue > 0.05 if len(groups) > 1 else True

        if all(len(group) > 3 for group in groups):
            if normality and homogeneity:
                try:
                    stat, pvalue = f_oneway(*groups)
                    return stat, pvalue, "ANOVA"
                except ValueError:
                    pass
        stat, pvalue = kruskal(*groups)
        return stat, pvalue, "Kruskal-Wallis"

    def select_features(self):
        """Effectue la sélection des caractéristiques en fonction des types de données."""
        while True:  # Boucle pour permettre à l'utilisateur de tester plusieurs méthodes
            X_types, y_type = self._check_variable_types()
            print(f"Types détectés : X -> {X_types}, y -> {y_type}")
            print(f"Dimensions de X : {self.X.shape}, Dimensions de y : {self.y.shape}")

            if len(self.X) != len(self.y):
                raise ValueError(f"Le nombre de lignes de X ({len(self.X)}) ne correspond pas à celui de y ({len(self.y)})")

            self.results.clear()  # Réinitialiser les résultats à chaque test de nouvelle méthode

            if all(val == 'catégorielle' for val in X_types.values()) and y_type == 'catégorielle':
                print("1 : Chi-square\n2 : Information Mutuelle")
                method_choice = int(input("Entrez le numéro correspondant à votre choix : ").strip())
                if method_choice == 1:
                    self.results.update(self._apply_chi2_test())
                else:
                    self.results = self._apply_mutual_info(is_categorical_y=True)

            elif all(val == 'numérique' for val in X_types.values()) and y_type == 'numérique':
                print("1 : Spearman\n2 : Information Mutuelle")
                method_choice = int(input("Entrez le numéro correspondant à votre choix : ").strip())
                if method_choice == 1:
                    for col in self.X.columns:
                        stat, pvalue = self._apply_spearman_correlation(col)
                        self.results[col] = {'méthode': 'Spearman', 'stat': stat, 'pvalue': pvalue}
                else:
                    self.results = self._apply_mutual_info(is_categorical_y=False)

            elif all(val == 'numérique' for val in X_types.values()) and y_type == 'catégorielle':
                print("1 : ANOVA\n2 : Information Mutuelle")
                method_choice = int(input("Entrez le numéro correspondant à votre choix : ").strip())
                if method_choice == 1:
                    for col in self.X.columns:
                        stat, pvalue, method = self._apply_anova_or_kruskal(col)
                        self.results[col] = {'méthode': method, 'stat': stat, 'pvalue': pvalue}
                else:
                    self.results = self._apply_mutual_info(is_categorical_y=True)

            else:
                print("Variables mixtes, application automatique de l'Information Mutuelle.")
                self.results = self._apply_mutual_info(is_categorical_y=(y_type == 'catégorielle'))

            # Sélection des features
            if "Information Mutuelle" in [res['méthode'] for res in self.results.values()]:
                # Si la méthode utilisée est Information Mutuelle, garder uniquement les k meilleures features choisies par l'utilisateur
                self.selected_features = list(self.results.keys())
            else:
                # Sinon, utiliser le critère classique (pvalue < 0.05)
                self.selected_features = [col for col, res in self.results.items() if 'pvalue' in res and res['pvalue'] < 0.05]

            self.removed_features = [col for col in self.X.columns if col not in self.selected_features]

            # Affichage des résultats
            print(pd.DataFrame(self.results).T)
            print(f"Les variables caractéristiques sélectionnées : {self.selected_features}")
            print(f"Les variables caractéristiques supprimées : {self.removed_features}")

            # Demander à l'utilisateur s'il veut garder les variables sélectionnées ou essayer une autre méthode
            choice = int(input(
                "1. Garder les variables sélectionnées puis continuer.\n"
                "2. Essayer une autre méthode de feature selection.\n"
                "3. Ignorer l'étape de feature selection puis continuer.\n"
                "Veuillez choisir une option (1-3) : ").strip())

            if choice == 1:
                return self.X[self.selected_features], self.y
            elif choice == 3:
                print("L'étape de sélection des variables statistiquement plus importante de votre dataset a été ignorée.")
                return self.X, self.y
            # Si choice == 2, on recommence la boucle et l'utilisateur peut tester une autre méthode


    def run_feature_selection(self, X_feature, y_target):
        print("______________Sélection des variables les plus importantes...\n ______________")
        selector = FeatureSelector(X_feature, y_target)
        X_selected, y_selected = selector.select_features()
        print("Dimensions finales de X :", X_selected.shape)
        print("Dimensions finales de y :", y_selected.shape)
        return X_selected, y_selected
