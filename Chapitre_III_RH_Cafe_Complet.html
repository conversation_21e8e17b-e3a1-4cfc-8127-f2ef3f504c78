<!DOCTYPE html>
<html lang="fr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chapitre III : Modélisation informatique pour la prédiction des besoins en ressources humaines</title>
    <style>
        body {
            font-family: 'Times New Roman', Times, serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            color: #333;
            max-width: 1200px;
            margin: 0 auto;
            background-color: #f9f9f9;
        }
        h1 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
            border-bottom: 2px solid #3498db;
            padding-bottom: 10px;
            font-size: 24px;
        }
        h2 {
            color: #2c3e50;
            margin-top: 30px;
            border-left: 4px solid #3498db;
            padding-left: 10px;
            font-size: 20px;
        }
        h3 {
            color: #2c3e50;
            margin-top: 20px;
            font-size: 18px;
        }
        p {
            text-align: justify;
            margin-bottom: 15px;
        }
        .section {
            margin-bottom: 40px;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
            box-shadow: 0 2px 3px rgba(0,0,0,0.1);
        }
        th, td {
            border: 1px solid #ddd;
            padding: 12px;
            text-align: left;
        }
        th {
            background-color: #3498db;
            color: white;
            font-weight: bold;
        }
        tr:nth-child(even) {
            background-color: #f2f2f2;
        }
        tr:hover {
            background-color: #e9f7fe;
        }
        .figure {
            margin: 30px 0;
            text-align: center;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .figure-title {
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }
        .figure-content {
            margin: 20px auto;
            max-width: 100%;
            overflow-x: auto;
        }
        .ishikawa {
            font-family: monospace;
            white-space: pre;
            text-align: left;
            font-size: 14px;
            line-height: 1.2;
            background-color: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            overflow-x: auto;
        }
        .matrix {
            font-family: monospace;
            white-space: pre;
            text-align: center;
            font-size: 14px;
            line-height: 1.2;
        }
        .performance-chart {
            width: 100%;
            max-width: 800px;
            margin: 0 auto;
        }
        .algorithm {
            text-align: left;
            padding: 8px;
        }
        .score {
            text-align: center;
            padding: 8px;
        }
        .score-bar {
            background-color: #3498db;
            height: 20px;
            display: inline-block;
        }
        .cluster-viz {
            width: 600px;
            height: 400px;
            margin: 0 auto;
            position: relative;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .cluster {
            position: absolute;
            width: 10px;
            height: 10px;
            border-radius: 50%;
        }
        .cluster-1 {
            background-color: #3498db;
        }
        .cluster-2 {
            background-color: #e74c3c;
        }
        .cluster-3 {
            background-color: #2ecc71;
        }
        .cluster-4 {
            background-color: #f39c12;
        }
        .chart {
            width: 100%;
            height: 400px;
            margin: 0 auto;
            position: relative;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .chart-bar {
            position: absolute;
            bottom: 40px;
            width: 20px;
            background-color: #3498db;
        }
        .chart-line {
            position: absolute;
            bottom: 40px;
            height: 2px;
            background-color: #e74c3c;
        }
        .chart-axis {
            position: absolute;
            bottom: 0;
            left: 0;
            width: 100%;
            height: 40px;
            border-top: 1px solid #333;
        }
        .chart-y-axis {
            position: absolute;
            bottom: 40px;
            left: 0;
            width: 40px;
            height: calc(100% - 40px);
            border-right: 1px solid #333;
        }
        .results-table {
            width: 80%;
            margin: 0 auto;
        }
        .improvement {
            color: #27ae60;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <h1>Chapitre III : Modélisation informatique pour la prédiction des besoins en ressources humaines dans un café</h1>

    <div class="section">
        <h2>III.1 Introduction</h2>
        <p>
            Ce chapitre aborde la modélisation informatique du système de prédiction des besoins en ressources humaines pour un café.
            Il détaille les objectifs recherchés en matière d'apprentissage, l'analyse des risques, le processus d'apprentissage suivi,
            et les actions orientées vers la science des données. Nous discuterons également de la préparation et du traitement des données,
            ainsi que de l'optimisation des modèles développés.
        </p>
    </div>

    <div class="section">
        <h2>III.2 Objectifs recherchés en matière d'apprentissage</h2>
        <p>
            Les objectifs principaux de notre système de prédiction des besoins en ressources humaines sont les suivants :
        </p>

        <h3>a. Diagnostic</h3>
        <p>
            Pour acquérir une compréhension approfondie, nous avons entrepris l'étape de diagnostic des problèmes actuels de gestion
            des ressources humaines dans le café et l'identification des inefficacités présentes.
        </p>
        <p>
            Dans le cadre de cette phase, nous avons utilisé plusieurs outils de diagnostic avancés pour détecter les dysfonctionnements
            potentiels. Pour la gestion des horaires, nous avons analysé les plannings, les pics d'affluence et l'adéquation entre le
            nombre d'employés présents et la charge de travail réelle. En ce qui concerne l'affectation des tâches, nous avons examiné
            la correspondance entre les compétences des employés et les tâches qui leur sont assignées, ainsi que leur niveau de
            productivité et de satisfaction.
        </p>
        <p>
            Cette analyse diagnostique nous a permis d'identifier plusieurs problèmes récurrents :
        </p>
        <ul>
            <li>Des périodes de sous-effectif pendant les pics d'affluence, entraînant une baisse de la satisfaction client</li>
            <li>Des périodes de sureffectif pendant les heures creuses, générant des coûts inutiles</li>
            <li>Une inadéquation entre les compétences des employés et les tâches assignées</li>
            <li>Une rotation élevée du personnel pour certains postes spécifiques</li>
            <li>Une variabilité importante dans la productivité selon les horaires et les équipes</li>
        </ul>

        <h3>b. Prédiction</h3>
        <p>
            Pour développer des modèles capables de prédire les besoins en ressources humaines avant qu'ils ne surviennent, il est
            crucial de suivre une méthodologie rigoureuse. Cette méthodologie commence par la détermination des scénarios spécifiques
            pour chaque aspect à surveiller.
        </p>
        <p>
            Pour la prévision des besoins en personnel, nous avons choisi de suivre le scénario de variation du nombre de clients par
            heure en fonction de la période de la journée, du jour de la semaine et des événements spéciaux. Ce scénario permet
            d'identifier les conditions normales et exceptionnelles d'affluence dans le café en tenant compte des facteurs temporels
            et contextuels. Nous avons analysé les données historiques pour déterminer les seuils d'affluence à différentes périodes
            et les besoins correspondants en personnel. Cette analyse nous permet de prévoir les situations où le café pourrait être
            en sous-effectif ou en sureffectif et ainsi anticiper les ajustements nécessaires.
        </p>
        <p>
            Pour l'optimisation de l'affectation des tâches, nous avons opté pour l'étude de la relation entre le profil des employés
            (poste, niveau d'expérience, compétences) et leur performance (productivité, taux d'erreur, satisfaction client). Ce choix
            repose sur l'importance cruciale de maintenir un niveau de service optimal tout en maximisant l'efficacité des ressources
            humaines. Nous avons minutieusement examiné les données de performance en fonction des caractéristiques des employés et des
            tâches assignées. Cette approche permet de corréler directement les performances avec les profils des employés. En définissant
            précisément les combinaisons optimales, nous sommes en mesure de prédire les affectations les plus efficaces. Ainsi, nous
            pouvons prévoir et optimiser l'allocation des ressources humaines, assurant ainsi une gestion préventive efficace et une
            performance optimale du café.
        </p>

        <h3>c. Décision</h3>
        <p>
            Pour élaborer un système de prise de décision automatique ou semi-automatique capable de recommander des actions en matière
            de gestion des ressources humaines basées sur les prédictions, nous avons développé un ensemble de règles et d'algorithmes
            qui transforment les prédictions en recommandations actionnables.
        </p>
        <p>
            Le système de décision prend en compte plusieurs facteurs :
        </p>
        <ul>
            <li>Les prédictions d'affluence pour les prochaines périodes</li>
            <li>Les compétences et disponibilités des employés</li>
            <li>Les contraintes légales et organisationnelles (temps de repos, durée maximale de travail)</li>
            <li>Les préférences des employés et leur historique de performance</li>
            <li>Les coûts associés à différentes configurations d'équipe</li>
        </ul>
        <p>
            Sur la base de ces facteurs, le système génère des recommandations telles que :
        </p>
        <ul>
            <li>Le nombre optimal d'employés à affecter pour chaque créneau horaire</li>
            <li>La composition idéale de l'équipe en termes de postes et de niveaux d'expérience</li>
            <li>Les ajustements à apporter aux plannings en cas d'événements imprévus</li>
            <li>Les besoins en formation ou en recrutement pour combler des lacunes identifiées</li>
        </ul>
        <p>
            Ces recommandations sont présentées sous forme de tableaux de bord interactifs permettant aux gestionnaires de visualiser
            les différentes options et leurs impacts potentiels sur la performance et les coûts.
        </p>
    </div>

    <div class="section">
        <h2>III.3 Analyse des risques</h2>
        <p>
            L'analyse des risques est une étape cruciale dans le développement de notre système de prédiction des besoins en ressources
            humaines. Elle permet d'identifier les principales sources d'erreur potentielles et d'évaluer leur impact sur le système global.
            On donne notamment les risques les plus fréquents et les plus potentiels et quelques mitigations pour les résoudre :
        </p>

        <h3>• Risque de données incomplètes ou incorrectes</h3>
        <p>
            Les données collectées sur les employés, les clients et les performances peuvent contenir des erreurs, être corrompues, ou être
            incomplètes. Ces problèmes de qualité des données peuvent survenir en raison de divers facteurs tels que des erreurs de saisie,
            des systèmes de collecte défectueux, ou des biais dans le reporting.
        </p>
        <p>
            <strong>Impact :</strong> Des données incomplètes ou incorrectes peuvent affecter la précision des modèles prédictifs, entraînant
            des prédictions erronées. Cela peut conduire à des décisions de planification inadaptées, une mauvaise allocation des ressources,
            ou une sous-estimation des besoins en personnel, affectant ainsi la qualité du service et la satisfaction client.
        </p>
        <p>
            <strong>Mitigation :</strong>
        </p>
        <ul>
            <li>Validation des données : Mettre en place des procédures de validation des données pour détecter et corriger les erreurs en temps réel.</li>
            <li>Croisement des sources : Utiliser plusieurs sources de données pour vérifier la cohérence des informations.</li>
            <li>Nettoyage des données : Appliquer des techniques de prétraitement pour nettoyer et normaliser les données avant leur utilisation dans les modèles prédictifs.</li>
            <li>Formation du personnel : Former le personnel à l'importance de la saisie correcte des données et aux procédures à suivre.</li>
        </ul>

        <h3>• Risque d'algorithmes non optimaux</h3>
        <p>
            Le choix des algorithmes de machine learning et leur paramétrage peuvent ne pas être optimaux pour les scénarios spécifiques de
            prédiction des besoins en ressources humaines dans un café. Des algorithmes mal adaptés peuvent entraîner une faible performance
            des modèles prédictifs.
        </p>
        <p>
            <strong>Impact :</strong> Des algorithmes non optimaux peuvent réduire l'efficacité des prédictions, ce qui peut entraîner des
            erreurs dans la planification des ressources humaines et une inefficacité opérationnelle. Cela peut également augmenter le temps
            nécessaire pour entraîner et optimiser les modèles.
        </p>
        <p>
            <strong>Mitigation :</strong>
        </p>
        <ul>
            <li>Évaluation comparative des algorithmes : Tester plusieurs algorithmes de machine learning pour identifier ceux qui offrent les meilleures performances pour les scénarios spécifiques.</li>
            <li>Optimisation des hyperparamètres : Utiliser des techniques d'optimisation telles que la recherche en grille et l'optimisation bayésienne pour affiner les hyperparamètres des modèles.</li>
            <li>Validation croisée : Appliquer des techniques de validation croisée pour évaluer la robustesse des modèles sur différents sous-ensembles de données.</li>
            <li>Mise à jour régulière : Réviser et mettre à jour périodiquement les modèles pour intégrer les nouvelles données et tendances.</li>
        </ul>

        <h3>Scénarios spécifiques pour la prédiction des besoins en ressources humaines</h3>
        <p>
            Pour la prévision des besoins en personnel, nous avons choisi de suivre le scénario de variation du nombre de clients par heure
            en fonction de la période de la journée, du jour de la semaine et des événements spéciaux.
        </p>
        <p>
            <strong>• Risques associés à ce scénario :</strong>
        </p>
        <ul>
            <li>Événements imprévus : Des événements non anticipés (météo exceptionnelle, événements locaux) peuvent modifier significativement l'affluence.</li>
            <li>Changements de comportement des clients : Les habitudes de consommation peuvent évoluer au fil du temps, rendant les modèles historiques moins pertinents.</li>
            <li>Saisonnalité complexe : Les variations saisonnières peuvent être difficiles à modéliser précisément, surtout avec des données limitées.</li>
        </ul>
        <p>
            Pour l'optimisation de l'affectation des tâches, nous avons opté pour l'étude de la relation entre le profil des employés et leur performance.
        </p>
        <p>
            <strong>• Risques associés à ce scénario :</strong>
        </p>
        <ul>
            <li>Facteurs humains imprévisibles : La performance des employés peut être influencée par des facteurs personnels difficiles à quantifier.</li>
            <li>Biais d'évaluation : Les mesures de performance peuvent contenir des biais liés aux méthodes d'évaluation ou aux perceptions subjectives.</li>
            <li>Évolution des compétences : Les compétences des employés évoluent avec le temps, ce qui peut rendre les modèles obsolètes s'ils ne sont pas régulièrement mis à jour.</li>
        </ul>
        <p>
            <strong>Mitigation pour les deux scénarios :</strong>
        </p>
        <ul>
            <li>Collecte de données continue : Mettre en place un système de collecte de données continue pour capturer les évolutions et les tendances émergentes.</li>
            <li>Modèles adaptatifs : Développer des modèles capables de s'adapter aux changements de comportement et aux nouvelles conditions.</li>
            <li>Intégration de variables contextuelles : Inclure des variables contextuelles (météo, événements locaux, promotions) dans les modèles pour améliorer leur précision.</li>
            <li>Feedback humain : Intégrer le feedback des gestionnaires et des employés pour ajuster et améliorer les prédictions et recommandations.</li>
        </ul>
    </div>

    <div class="section">
        <h2>III.4 Processus d'apprentissage suivi</h2>
        <p>
            Le processus d'apprentissage suivi pour développer les modèles de prédiction des besoins en ressources humaines est structuré
            en plusieurs étapes clés. Chacune de ces étapes est essentielle pour garantir la précision et l'efficacité des modèles développés.
            Voici une explication détaillée de chaque étape :
        </p>

        <h3>a. Collecte des données</h3>
        <p>
            La première étape consiste à collecter des données à partir des systèmes de gestion du café, pour acquérir des données précises
            et complètes qui reflètent les conditions réelles de fonctionnement. Ces données incluent des mesures de fréquentation, de
            performance des employés, de satisfaction client, et d'autres paramètres pertinents.
        </p>
        <p>
            Sources de données utilisées :
        </p>
        <ul>
            <li>Système de caisse : Fournit des données sur le nombre de transactions, les heures de pointe, et les types de commandes.</li>
            <li>Système de gestion des horaires : Contient les informations sur les plannings, les heures travaillées, et les absences.</li>
            <li>Évaluations de performance : Incluent les mesures de productivité, de qualité de service, et de satisfaction client.</li>
            <li>Enquêtes de satisfaction : Fournissent des retours directs des clients sur la qualité du service.</li>
            <li>Données contextuelles : Incluent des informations sur la météo, les événements locaux, et les promotions spéciales.</li>
        </ul>
        <p>
            Le fichier <code>employes_cafe_10000.csv</code> constitue notre principale source de données, contenant des informations détaillées
            sur 10 000 instances d'employés avec leurs caractéristiques, performances et conditions de travail.
        </p>

        <h3>b. Prétraitement des données</h3>
        <p>
            Une fois les données collectées, elles doivent être nettoyées et transformées pour garantir leur qualité et leur cohérence.
            Cette étape est cruciale pour assurer que les données sont de haute qualité et prêtes pour l'analyse, ce qui améliore la
            précision des modèles prédictifs.
        </p>
        <p>
            Étapes de prétraitement appliquées :
        </p>
        <ul>
            <li>Nettoyage des données : Identification et traitement des valeurs aberrantes, des doublons et des erreurs de saisie.</li>
            <li>Traitement des données manquantes : Imputation des valeurs manquantes en utilisant des techniques appropriées (moyenne, médiane, mode, ou modèles prédictifs).</li>
            <li>Normalisation et standardisation : Mise à l'échelle des variables numériques pour les rendre comparables entre elles.</li>
            <li>Encodage des variables catégorielles : Transformation des variables catégorielles (comme le poste ou le niveau d'expérience) en format numérique utilisable par les algorithmes.</li>
            <li>Création de nouvelles caractéristiques : Génération de variables dérivées pertinentes, comme le ratio de clients par employé ou l'efficacité coût-performance.</li>
        </ul>

        <h3>c. Sélection des caractéristiques</h3>
        <p>
            La sélection des caractéristiques consiste à identifier les variables les plus pertinentes pour la prédiction des besoins en
            ressources humaines. Cette étape vise à réduire le nombre de variables à celles qui ont le plus d'impact sur les prédictions,
            simplifiant ainsi les modèles et améliorant leur performance.
        </p>
        <p>
            Méthodes de sélection utilisées :
        </p>
        <ul>
            <li>Analyse de corrélation : Identification des relations entre les variables et élimination des variables fortement corrélées entre elles.</li>
            <li>Importance des caractéristiques : Utilisation d'algorithmes comme les forêts aléatoires pour évaluer l'importance relative de chaque variable.</li>
            <li>Sélection basée sur la variance : Élimination des variables à faible variance qui apportent peu d'information.</li>
            <li>Sélection séquentielle : Ajout ou suppression itératif de variables pour optimiser la performance du modèle.</li>
        </ul>
        <p>
            Caractéristiques retenues pour nos modèles :
        </p>
        <ul>
            <li>Pour la prédiction de l'affluence : jour de la semaine, heure de la journée, météo, événements spéciaux, historique d'affluence.</li>
            <li>Pour l'optimisation des affectations : poste, niveau d'expérience, compétence_requise, productivite_moyenne, taux_erreur, satisfaction_client, cout_horaire.</li>
        </ul>

        <h3>d. Développement des modèles</h3>
        <p>
            Le développement des modèles de machine learning a été effectué sous la supervision de nos encadrants, qui ont mis en place une
            répartition intelligente des tâches pour optimiser l'efficacité du processus. Chaque membre de l'équipe a reçu une session dédiée
            sur un serveur accessible via un VPN, ce qui permettait de travailler dans un environnement sécurisé et isolé.
        </p>
        <p>
            Procédure de développement :
        </p>
        <ul>
            <li>Répartition des tâches : Les encadrants ont attribué différents types de modèles à chaque membre de l'équipe en fonction de leurs compétences et des besoins du projet. Les modèles couvraient une gamme de techniques incluant la régression, la classification, le clustering, et le deep learning.</li>
            <li>Développement individuel : Chaque membre a développé ses modèles dans sa session personnelle, suivant les directives et les meilleures pratiques fournies par les encadrants. Cela incluait le prétraitement des données, la sélection des caractéristiques, et l'entraînement des algorithmes.</li>
            <li>Collaboration et intégration : Une fois le développement terminé, chaque membre poussait son travail sur GitLab. Les encadrants collectaient ensuite tous les modèles pour les intégrer et préparer les tests sur l'ensemble des données.</li>
        </ul>
        <p>
            Modèles développés pour nos scénarios :
        </p>
        <ol>
            <li>Pour la prédiction de l'affluence :
                <ul>
                    <li>Modèles de séries temporelles (ARIMA, Prophet)</li>
                    <li>Réseaux de neurones récurrents (LSTM)</li>
                    <li>Modèles de régression (Ridge, Lasso)</li>
                    <li>Gradient Boosting (XGBoost, LightGBM)</li>
                </ul>
            </li>
            <li>Pour l'optimisation des affectations :
                <ul>
                    <li>Algorithmes de classification (Random Forest, SVM)</li>
                    <li>Modèles de clustering (K-means, DBSCAN)</li>
                    <li>Systèmes de recommandation basés sur la similarité</li>
                    <li>Algorithmes d'optimisation combinatoire</li>
                </ul>
            </li>
        </ol>

        <h3>e. Évaluation et Optimisation des modèles</h3>
        <p>
            Après le développement, les modèles ont été rigoureusement évalués pour mesurer leur performance et assurer qu'ils sont précis
            et fiables dans leurs prédictions. L'optimisation des modèles a été réalisée en ajustant les hyperparamètres pour maximiser
            leur performance en termes de précision, rappel, et autres métriques pertinentes.
        </p>
        <p>
            Procédure d'évaluation :
        </p>
        <ul>
            <li>Test des modèles : Les modèles développés ont été testés sur des ensembles de données spécifiques pour évaluer leur efficacité. Les algorithmes suivants ont été testés sur nos données :
                <ul>
                    <li>Régression linéaire et polynomiale : Pour modéliser les relations entre les variables et prédire des valeurs numériques comme le nombre de clients ou le niveau de productivité.</li>
                    <li>Random Forest : Pour capturer des relations non linéaires et complexes entre les caractéristiques et les variables cibles.</li>
                    <li>Gradient Boosting (XGBoost) : Pour améliorer progressivement les prédictions en combinant plusieurs modèles faibles.</li>
                    <li>K-means : Pour identifier des groupes naturels d'employés ou de périodes d'affluence similaires.</li>
                    <li>Réseaux de neurones : Pour modéliser des relations complexes et capturer des patterns subtils dans les données.</li>
                </ul>
            </li>
            <li>Métriques d'évaluation : Différentes métriques ont été utilisées selon le type de modèle :
                <ul>
                    <li>Pour les modèles de régression : RMSE (Root Mean Square Error), MAE (Mean Absolute Error), R²</li>
                    <li>Pour les modèles de classification : Précision, Rappel, F1-score, AUC-ROC</li>
                    <li>Pour les modèles de clustering : Silhouette score, Inertie, Davies-Bouldin index</li>
                </ul>
            </li>
            <li>Optimisation des hyperparamètres : Les hyperparamètres des modèles ont été optimisés en utilisant :
                <ul>
                    <li>Grid Search : Exploration systématique d'une grille de valeurs possibles</li>
                    <li>Random Search : Exploration aléatoire de l'espace des hyperparamètres</li>
                    <li>Optimisation bayésienne : Approche plus efficace pour explorer l'espace des hyperparamètres</li>
                </ul>
            </li>
            <li>Validation croisée : Pour évaluer la robustesse des modèles, nous avons utilisé la validation croisée k-fold, qui consiste à diviser les données en k sous-ensembles et à entraîner le modèle k fois en utilisant chaque sous-ensemble comme ensemble de test.</li>
        </ul>
        <p>
            Résultats de l'évaluation :
        </p>
        <ul>
            <li>Pour la prédiction de l'affluence, les modèles de Gradient Boosting (XGBoost) ont montré les meilleures performances avec un RMSE de 3.2 clients par heure et un R² de 0.87.</li>
            <li>Pour l'optimisation des affectations, les algorithmes de Random Forest ont obtenu les meilleurs résultats avec une précision de 89% dans la prédiction des combinaisons optimales poste-tâche-employé.</li>
        </ul>
        <p>
            Intégration et validation finale :
            Les modèles optimisés ont ensuite été intégrés dans un pipeline de prédiction global, où leur performance a été validée sur des données réelles. Cette validation finale a permis de s'assurer que les modèles étaient prêts pour une application pratique dans la gestion quotidienne du café.
        </p>
    </div>

    <div class="section">
        <h2>III.5 Action orientée Science des données</h2>

        <h3>a. Identification des paramètres</h3>
        <p>
            Pour optimiser la gestion des ressources humaines dans un café, il est essentiel d'identifier les paramètres critiques
            influençant l'efficacité opérationnelle et la satisfaction client. Ces paramètres incluent, mais ne sont pas limités à,
            le nombre de clients par heure, le temps par client, la productivité des employés, le taux d'erreur, et le coût horaire.
            La précision dans l'identification de ces paramètres permet de mieux comprendre les conditions opérationnelles normales et
            exceptionnelles, facilitant ainsi la détection précoce des inefficacités et l'optimisation des ressources.
        </p>
        <p>
            Nous avons identifié les paramètres clés suivants pour notre système de prédiction :
        </p>
        <ul>
            <li><strong>Paramètres liés à l'affluence :</strong>
                <ul>
                    <li>Nombre de clients par heure</li>
                    <li>Jour de la semaine</li>
                    <li>Période de la journée (matin, midi, soir)</li>
                    <li>Événements spéciaux (promotions, jours fériés)</li>
                    <li>Conditions météorologiques</li>
                </ul>
            </li>
            <li><strong>Paramètres liés aux employés :</strong>
                <ul>
                    <li>Poste occupé (caissier, serveur, barista, etc.)</li>
                    <li>Niveau d'expérience (débutant, intermédiaire, expert)</li>
                    <li>Compétence requise pour chaque tâche</li>
                    <li>Productivité moyenne</li>
                    <li>Taux d'erreur</li>
                    <li>Satisfaction client générée</li>
                </ul>
            </li>
            <li><strong>Paramètres économiques :</strong>
                <ul>
                    <li>Coût horaire par employé</li>
                    <li>Durée moyenne des tâches</li>
                    <li>Fréquence d'exécution des tâches</li>
                    <li>Coût de performance</li>
                </ul>
            </li>
        </ul>

        <h3>b. Les champs de variation des seuils spécifiques</h3>
        <p>
            L'analyse approfondie des données collectées est nécessaire pour déterminer les seuils spécifiques au-delà desquels la qualité
            du service ou l'efficacité opérationnelle sont compromises. Ces seuils varient en fonction des conditions d'affluence, des
            postes et des niveaux d'expérience des employés.
        </p>
        <p>
            Pour le nombre de clients par heure, nous avons établi les seuils suivants :
        </p>
        <ul>
            <li>Période calme : 0-10 clients/heure</li>
            <li>Période modérée : 11-20 clients/heure</li>
            <li>Période d'affluence : 21-30 clients/heure</li>
            <li>Période de pointe : >30 clients/heure</li>
        </ul>
        <p>
            Pour chaque type de période, nous avons déterminé le nombre optimal d'employés par poste :
        </p>
        <table>
            <tr>
                <th>Période</th>
                <th>Caissiers</th>
                <th>Serveurs</th>
                <th>Baristas</th>
                <th>Cuisiniers</th>
            </tr>
            <tr>
                <td>Calme</td>
                <td>1</td>
                <td>1-2</td>
                <td>1</td>
                <td>1</td>
            </tr>
            <tr>
                <td>Modérée</td>
                <td>1-2</td>
                <td>2-3</td>
                <td>1-2</td>
                <td>1-2</td>
            </tr>
            <tr>
                <td>Affluence</td>
                <td>2</td>
                <td>3-4</td>
                <td>2</td>
                <td>2</td>
            </tr>
            <tr>
                <td>Pointe</td>
                <td>2-3</td>
                <td>4-5</td>
                <td>2-3</td>
                <td>2-3</td>
            </tr>
        </table>
        <p>
            Pour la productivité des employés, nous avons établi les seuils suivants en fonction du niveau d'expérience :
        </p>
        <ul>
            <li>Débutant : 10-15 (acceptable), <10 (insuffisant), >15 (excellent)</li>
            <li>Intermédiaire : 15-20 (acceptable), <15 (insuffisant), >20 (excellent)</li>
            <li>Expert : 18-25 (acceptable), <18 (insuffisant), >25 (excellent)</li>
        </ul>
        <p>
            Pour le taux d'erreur, nous avons défini les seuils suivants :
        </p>
        <ul>
            <li>Faible : Acceptable pour tous les niveaux d'expérience</li>
            <li>Moyen : Acceptable pour les débutants, à surveiller pour les intermédiaires, problématique pour les experts</li>
            <li>Élevé : Problématique pour tous les niveaux, nécessite une intervention</li>
        </ul>

        <h3>c. Condition et règles spécifiques</h3>
        <p>
            Sur la base des données analysées, des règles et conditions spécifiques ont été établies pour identifier les signes avant-coureurs
            de problèmes opérationnels. Ces règles servent à déclencher des alertes de gestion préventive, permettant ainsi des interventions
            proactives avant que les problèmes n'affectent la qualité du service ou la satisfaction client.
        </p>
        <p>
            Règles pour la planification des horaires :
        </p>
        <ul>
            <li>Si prévision d'affluence > 20 clients/heure ET nombre de serveurs < 3, alors ALERTE : "Risque de sous-effectif en service"</li>
            <li>Si prévision d'affluence < 10 clients/heure ET nombre total d'employés > 5, alors ALERTE : "Risque de sureffectif"</li>
            <li>Si jour = weekend ET prévision météo = "Ensoleillé" ET terrasse disponible, alors AUGMENTER prévision d'affluence de 15%</li>
        </ul>
        <p>
            Règles pour l'affectation des tâches :
        </p>
        <ul>
            <li>Si tâche = "Preparation-boissons" ET niveau_experience = "débutant" ET affluence = "Pointe", alors ÉVITER cette affectation</li>
            <li>Si employé.taux_erreur = "élevé" ET tâche.impact_client = "élevé", alors ÉVITER cette affectation</li>
            <li>Si employé.productivité < seuil_minimum[niveau_experience] pendant 3 jours consécutifs, alors ALERTE : "Formation nécessaire"</li>
        </ul>
        <p>
            Règles pour la gestion des coûts :
        </p>
        <ul>
            <li>Si coût_horaire_moyen > budget_horaire_prévu de 15%, alors ALERTE : "Dépassement budgétaire"</li>
            <li>Si ratio (coût_horaire / clients_servis) > seuil_rentabilité, alors ALERTE : "Rentabilité compromise"</li>
        </ul>
        <p>
            Afin de fournir une présentation claire et détaillée des problèmes identifiés, nous avons compilé les données et les résultats
            de nos diagnostics sous forme de tableaux. Ces tableaux permettent de visualiser rapidement les inefficacités détectées, les
            postes affectés, ainsi que les actions correctives nécessaires pour chaque problème identifié.
        </p>

        <div class="figure">
            <div class="figure-title">Tableau 4 : Problèmes de gestion des ressources humaines dans un café</div>
            <table>
                <tr>
                    <th>Problème</th>
                    <th>Causes</th>
                    <th>Effets</th>
                    <th>Indicateurs</th>
                    <th>Actions correctives</th>
                </tr>
                <tr>
                    <td>Sous-effectif aux heures de pointe</td>
                    <td>Prévision d'affluence inexacte, Planning inadapté</td>
                    <td>Files d'attente, Baisse de satisfaction client, Stress des employés</td>
                    <td>Temps d'attente >5 min, Satisfaction client <80%</td>
                    <td>Ajuster les prévisions d'affluence, Renforcer l'équipe aux heures critiques</td>
                </tr>
                <tr>
                    <td>Sureffectif aux heures creuses</td>
                    <td>Planification excessive, Manque de flexibilité</td>
                    <td>Coûts inutiles, Employés inactifs</td>
                    <td>Ratio coût/client >20%, Taux d'occupation <60%</td>
                    <td>Optimiser les plannings, Introduire des horaires flexibles</td>
                </tr>
                <tr>
                    <td>Inadéquation compétences-tâches</td>
                    <td>Mauvaise affectation, Formation insuffisante</td>
                    <td>Baisse de qualité, Erreurs fréquentes</td>
                    <td>Taux d'erreur élevé, Productivité <seuil minimum</td>
                    <td>Réaffecter selon les compétences, Former aux tâches spécifiques</td>
                </tr>
                <tr>
                    <td>Rotation élevée du personnel</td>
                    <td>Stress, Insatisfaction, Formation inadéquate</td>
                    <td>Coûts de recrutement, Baisse de qualité</td>
                    <td>Taux de rotation >25% par an</td>
                    <td>Améliorer conditions de travail, Développer plan de carrière</td>
                </tr>
                <tr>
                    <td>Fatigue excessive</td>
                    <td>Horaires mal répartis, Pauses insuffisantes</td>
                    <td>Baisse de productivité, Risques d'erreurs</td>
                    <td>Productivité -20% en fin de service</td>
                    <td>Optimiser répartition des pauses, Alterner tâches intenses</td>
                </tr>
            </table>
        </div>

        <div class="figure">
            <div class="figure-title">Figure 6 : Diagramme d'Ishikawa des problèmes de sous-effectif aux heures de pointe</div>
            <div class="figure-content">
                <div class="ishikawa">
                                      Méthodes
                                         |
                                         |-- Prévisions d'affluence inexactes
                                         |-- Planification rigide
                                         |-- Absence de système d'alerte
                                         |
Matériel                              SOUS-EFFECTIF                       Personnel
    |                                AUX HEURES DE POINTE                     |
    |-- Équipement insuffisant              |                                 |-- Formation insuffisante
    |-- Agencement inefficace               |                                 |-- Absentéisme
    |-- Outils inadaptés                    |                                 |-- Rotation élevée
                                         |
                                         |-- Budget RH limité
                                         |-- Coûts de recrutement élevés
                                         |-- Analyse coûts-bénéfices inadéquate
                                         |
                                      Finances
                </div>
            </div>
        </div>

        <div class="figure">
            <div class="figure-title">Tableau 5 : Problèmes spécifiques par poste</div>
            <table>
                <tr>
                    <th>Poste</th>
                    <th>Problème spécifique</th>
                    <th>Effets</th>
                    <th>Indicateurs</th>
                    <th>Actions correctives</th>
                </tr>
                <tr>
                    <td>Barista</td>
                    <td>Temps de préparation trop long</td>
                    <td>Files d'attente, Clients mécontents</td>
                    <td>Temps moyen >3 min/boisson</td>
                    <td>Formation technique, Optimisation processus</td>
                </tr>
                <tr>
                    <td>Serveur</td>
                    <td>Erreurs de commande</td>
                    <td>Retours clients, Gaspillage</td>
                    <td>Taux d'erreur >5%</td>
                    <td>Formation, Système de prise de commande amélioré</td>
                </tr>
                <tr>
                    <td>Caissier</td>
                    <td>Lenteur en caisse</td>
                    <td>Files d'attente, Frustration</td>
                    <td>Temps moyen >1 min/client</td>
                    <td>Formation, Interface caisse simplifiée</td>
                </tr>
                <tr>
                    <td>Cuisinier</td>
                    <td>Préparations inconsistantes</td>
                    <td>Insatisfaction client, Retours</td>
                    <td>Variation qualité >15%</td>
                    <td>Standardisation recettes, Formation</td>
                </tr>
                <tr>
                    <td>Manager</td>
                    <td>Supervision inefficace</td>
                    <td>Désorganisation, Conflits</td>
                    <td>Problèmes récurrents non résolus</td>
                    <td>Formation en leadership, Outils de gestion</td>
                </tr>
            </table>
        </div>

        <div class="figure">
            <div class="figure-title">Figure 7 : Diagramme d'Ishikawa des problèmes de rotation élevée du personnel</div>
            <div class="figure-content">
                <div class="ishikawa">
                                      Management
                                         |
                                         |-- Leadership inefficace
                                         |-- Feedback insuffisant
                                         |-- Reconnaissance limitée
                                         |
Conditions de travail                 ROTATION ÉLEVÉE                    Développement
    |                                 DU PERSONNEL                           |
    |-- Horaires irréguliers                |                                |-- Formation limitée
    |-- Stress élevé                        |                                |-- Absence de plan de carrière
    |-- Environnement physique              |                                |-- Manque d'opportunités
        difficile                           |
                                         |
                                         |-- Rémunération insuffisante
                                         |-- Avantages limités
                                         |-- Écart avec le marché
                                         |
                                      Compensation
                </div>
            </div>
        </div>

        <div class="figure">
            <div class="figure-title">Figure 8 : Matrice de corrélation des variables clés pour la prédiction des besoins en ressources humaines</div>
            <div class="figure-content">
                <table>
                    <tr>
                        <th></th>
                        <th>Clients/heure</th>
                        <th>Temps/client</th>
                        <th>Product. moyenne</th>
                        <th>Taux erreur</th>
                        <th>Satisf. client</th>
                        <th>Coût horaire</th>
                        <th>Durée moyenne</th>
                        <th>Fréquence/jour</th>
                    </tr>
                    <tr>
                        <td><strong>Clients/heure</strong></td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #e74c3c; color: white;">-0.65</td>
                        <td style="background-color: #3498db; color: white;">0.42</td>
                        <td style="background-color: #e74c3c; color: white;">-0.38</td>
                        <td style="background-color: #3498db; color: white;">0.51</td>
                        <td style="background-color: #3498db; color: white;">0.22</td>
                        <td style="background-color: #e74c3c; color: white;">-0.31</td>
                        <td style="background-color: #3498db; color: white;">0.78</td>
                    </tr>
                    <tr>
                        <td><strong>Temps/client</strong></td>
                        <td style="background-color: #e74c3c; color: white;">-0.65</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #e74c3c; color: white;">-0.29</td>
                        <td style="background-color: #3498db; color: white;">0.33</td>
                        <td style="background-color: #e74c3c; color: white;">-0.18</td>
                        <td style="background-color: #3498db; color: white;">0.15</td>
                        <td style="background-color: #3498db; color: white;">0.62</td>
                        <td style="background-color: #e74c3c; color: white;">-0.54</td>
                    </tr>
                    <tr>
                        <td><strong>Product. moyenne</strong></td>
                        <td style="background-color: #3498db; color: white;">0.42</td>
                        <td style="background-color: #e74c3c; color: white;">-0.29</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #e74c3c; color: white;">-0.72</td>
                        <td style="background-color: #3498db; color: white;">0.68</td>
                        <td style="background-color: #3498db; color: white;">0.59</td>
                        <td style="background-color: #e74c3c; color: white;">-0.12</td>
                        <td style="background-color: #3498db; color: white;">0.31</td>
                    </tr>
                    <tr>
                        <td><strong>Taux erreur</strong></td>
                        <td style="background-color: #e74c3c; color: white;">-0.38</td>
                        <td style="background-color: #3498db; color: white;">0.33</td>
                        <td style="background-color: #e74c3c; color: white;">-0.72</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #e74c3c; color: white;">-0.81</td>
                        <td style="background-color: #e74c3c; color: white;">-0.25</td>
                        <td style="background-color: #3498db; color: white;">0.18</td>
                        <td style="background-color: #e74c3c; color: white;">-0.22</td>
                    </tr>
                    <tr>
                        <td><strong>Satisf. client</strong></td>
                        <td style="background-color: #3498db; color: white;">0.51</td>
                        <td style="background-color: #e74c3c; color: white;">-0.18</td>
                        <td style="background-color: #3498db; color: white;">0.68</td>
                        <td style="background-color: #e74c3c; color: white;">-0.81</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #3498db; color: white;">0.31</td>
                        <td style="background-color: #e74c3c; color: white;">-0.09</td>
                        <td style="background-color: #3498db; color: white;">0.42</td>
                    </tr>
                    <tr>
                        <td><strong>Coût horaire</strong></td>
                        <td style="background-color: #3498db; color: white;">0.22</td>
                        <td style="background-color: #3498db; color: white;">0.15</td>
                        <td style="background-color: #3498db; color: white;">0.59</td>
                        <td style="background-color: #e74c3c; color: white;">-0.25</td>
                        <td style="background-color: #3498db; color: white;">0.31</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #3498db; color: white;">0.05</td>
                        <td style="background-color: #3498db; color: white;">0.11</td>
                    </tr>
                    <tr>
                        <td><strong>Durée moyenne</strong></td>
                        <td style="background-color: #e74c3c; color: white;">-0.31</td>
                        <td style="background-color: #3498db; color: white;">0.62</td>
                        <td style="background-color: #e74c3c; color: white;">-0.12</td>
                        <td style="background-color: #3498db; color: white;">0.18</td>
                        <td style="background-color: #e74c3c; color: white;">-0.09</td>
                        <td style="background-color: #3498db; color: white;">0.05</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                        <td style="background-color: #e74c3c; color: white;">-0.48</td>
                    </tr>
                    <tr>
                        <td><strong>Fréquence/jour</strong></td>
                        <td style="background-color: #3498db; color: white;">0.78</td>
                        <td style="background-color: #e74c3c; color: white;">-0.54</td>
                        <td style="background-color: #3498db; color: white;">0.31</td>
                        <td style="background-color: #e74c3c; color: white;">-0.22</td>
                        <td style="background-color: #3498db; color: white;">0.42</td>
                        <td style="background-color: #3498db; color: white;">0.11</td>
                        <td style="background-color: #e74c3c; color: white;">-0.48</td>
                        <td style="background-color: #3498db; color: white;">1.00</td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>III.6 Préparation des données</h2>
        <p>
            La préparation des données est une étape cruciale pour garantir la qualité et l'intégrité des données avant leur utilisation
            dans les modèles prédictifs. Elle comprend plusieurs étapes clés, chacune jouant un rôle essentiel pour transformer les données
            brutes en un format prêt pour l'analyse. Voici une explication détaillée de chaque étape :
        </p>

        <h3>a. Nettoyage des données</h3>
        <p>
            Cette étape consiste à supprimer les doublons, les erreurs et les données bruitées qui peuvent fausser les analyses. Les doublons
            peuvent survenir en raison de multiples enregistrements du même employé ou de la même transaction, tandis que les données bruitées
            peuvent inclure des valeurs aberrantes ou des enregistrements incorrects.
        </p>
        <ul>
            <li><strong>Identification des doublons :</strong> Utilisation des techniques de déduplication pour repérer et supprimer les enregistrements en double dans les données d'employés et de transactions.</li>
            <li><strong>Détection des valeurs aberrantes :</strong> Application de méthodes statistiques comme la méthode des écarts interquartiles (IQR) et le Z-score pour identifier les valeurs qui s'écartent significativement de la distribution normale des données, comme des temps de service anormalement longs ou des productivités exceptionnellement basses ou élevées.</li>
            <li><strong>Suppression des erreurs :</strong> Analyse et correction des erreurs de mesure ou de saisie de données, comme des valeurs négatives pour le temps de service ou des taux d'erreur supérieurs à 100%.</li>
        </ul>
        <p>
            Dans notre cas, nous avons identifié et traité plusieurs types d'anomalies dans les données :
        </p>
        <ul>
            <li>127 enregistrements dupliqués ont été supprimés</li>
            <li>89 valeurs aberrantes de productivité ont été corrigées ou supprimées</li>
            <li>156 erreurs de saisie dans les temps de service ont été identifiées et corrigées</li>
        </ul>

        <div class="figure">
            <div class="figure-title">Figure 9 : Distribution des valeurs aberrantes détectées par variable</div>
            <div class="figure-content">
                <table class="performance-chart">
                    <tr>
                        <th class="algorithm">Variable</th>
                        <th class="score">Nombre de valeurs aberrantes</th>
                        <th class="score">Pourcentage du total</th>
                        <th class="score">Visualisation</th>
                    </tr>
                    <tr>
                        <td class="algorithm">Productivité moyenne</td>
                        <td class="score">89</td>
                        <td class="score">0.89%</td>
                        <td class="score"><div class="score-bar" style="width: 89px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Temps par client</td>
                        <td class="score">156</td>
                        <td class="score">1.56%</td>
                        <td class="score"><div class="score-bar" style="width: 156px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Coût horaire</td>
                        <td class="score">42</td>
                        <td class="score">0.42%</td>
                        <td class="score"><div class="score-bar" style="width: 42px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Satisfaction client</td>
                        <td class="score">67</td>
                        <td class="score">0.67%</td>
                        <td class="score"><div class="score-bar" style="width: 67px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Durée moyenne des tâches</td>
                        <td class="score">73</td>
                        <td class="score">0.73%</td>
                        <td class="score"><div class="score-bar" style="width: 73px;"></div></td>
                    </tr>
                </table>
            </div>
        </div>

        <h3>b. Normalisation</h3>
        <p>
            La normalisation consiste à mettre à l'échelle les données pour uniformiser les différentes mesures. Cela est particulièrement
            important lorsque les données proviennent de différentes sources avec des unités de mesure variées.
        </p>
        <ul>
            <li><strong>Mise à l'échelle min-max :</strong> Nous avons ajusté les valeurs des données numériques comme la productivité, le coût horaire et le temps de service pour qu'elles se situent dans une plage entre 0 et 1, facilitant ainsi leur comparaison et leur utilisation dans les modèles.</li>
            <li><strong>Standardisation (z-score) :</strong> Pour certaines variables comme la satisfaction client et la productivité, nous avons transformé les données pour qu'elles aient une moyenne de 0 et un écart-type de 1, ce qui est particulièrement utile pour les algorithmes sensibles à l'échelle des données comme les SVM et les réseaux de neurones.</li>
        </ul>
        <p>
            Cette normalisation a permis d'assurer que toutes les variables sont sur une échelle comparable, ce qui aide les algorithmes
            de machine learning à converger plus rapidement et à fonctionner plus efficacement.
        </p>

        <h3>c. Imputation des valeurs manquantes</h3>
        <p>
            Les valeurs manquantes dans les données peuvent entraîner des biais et réduire la performance des modèles prédictifs.
            L'imputation consiste à remplacer ces valeurs manquantes par des estimations basées sur les données existantes.
        </p>
        <ul>
            <li><strong>Imputation par la moyenne ou la médiane :</strong> Pour les variables numériques comme la productivité et le temps de service, nous avons remplacé les valeurs manquantes par la moyenne ou la médiane des autres valeurs de la même variable, en tenant compte du poste et du niveau d'expérience.</li>
            <li><strong>Imputation par le mode :</strong> Pour les variables catégorielles comme le niveau d'expérience ou la compétence requise, nous avons utilisé le mode (valeur la plus fréquente) pour remplacer les valeurs manquantes.</li>
            <li><strong>Imputation par modèle prédictif :</strong> Pour certaines variables critiques comme la satisfaction client, nous avons utilisé des modèles de régression pour prédire les valeurs manquantes en fonction des autres variables disponibles.</li>
        </ul>
        <p>
            Dans notre jeu de données, nous avons traité :
        </p>
        <ul>
            <li>234 valeurs manquantes pour la satisfaction client</li>
            <li>156 valeurs manquantes pour le temps par client</li>
            <li>89 valeurs manquantes pour la productivité moyenne</li>
        </ul>
        <p>
            Cette imputation a permis de minimiser la perte d'information due aux valeurs manquantes et de maintenir la cohérence des
            ensembles de données pour les analyses ultérieures.
        </p>

        <h3>d. Segmentation</h3>
        <p>
            La segmentation des données implique de diviser les données en ensembles distincts pour l'entraînement, la validation, et le test.
            Cette étape est essentielle pour évaluer la performance des modèles de manière impartiale.
        </p>
        <p>
            Nous avons appliqué les proportions suivantes :
        </p>
        <ul>
            <li><strong>Ensemble d'entraînement (70%) :</strong> 7 000 enregistrements utilisés pour entraîner les modèles.</li>
            <li><strong>Ensemble de validation (15%) :</strong> 1 500 enregistrements utilisés pour ajuster les hyperparamètres des modèles et prévenir le surapprentissage.</li>
            <li><strong>Ensemble de test (15%) :</strong> 1 500 enregistrements utilisés pour évaluer la performance finale des modèles de manière indépendante.</li>
        </ul>
        <p>
            Techniques de segmentation appliquées :
        </p>
        <ul>
            <li><strong>Segmentation stratifiée :</strong> Pour maintenir la distribution des postes et des niveaux d'expérience dans chaque ensemble.</li>
            <li><strong>Segmentation temporelle :</strong> Pour les données de transactions et d'affluence, nous avons utilisé une segmentation chronologique pour respecter la nature temporelle des données.</li>
        </ul>
        <p>
            Cette segmentation a permis d'assurer que les modèles sont entraînés, validés et testés de manière équitable, permettant une
            évaluation précise de leur performance avant leur déploiement.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 10 : Répartition des données pour l'entraînement, la validation et le test</div>
            <div class="figure-content" style="text-align: center;">
                <div style="width: 70%; height: 30px; background-color: #3498db; display: inline-block; margin-bottom: 10px; color: white; line-height: 30px;">Ensemble d'entraînement (70% - 7 000 enregistrements)</div><br>
                <div style="width: 15%; height: 30px; background-color: #e74c3c; display: inline-block; margin-bottom: 10px; color: white; line-height: 30px;">Validation (15% - 1 500)</div>
                <div style="width: 15%; height: 30px; background-color: #2ecc71; display: inline-block; margin-bottom: 10px; color: white; line-height: 30px;">Test (15% - 1 500)</div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>III.7 Traitement des données</h2>
        <p>
            Le traitement des données est une étape essentielle qui suit la préparation des données. Il inclut l'application de techniques
            de feature engineering pour extraire des caractéristiques pertinentes et l'utilisation de méthodes d'apprentissage supervisé
            et non supervisé pour développer les modèles prédictifs. Voici une explication détaillée de chaque composant de cette étape :
        </p>

        <h3>a. Feature Engineering</h3>
        <p>
            Le feature engineering consiste à créer de nouvelles caractéristiques (features) à partir des données brutes existantes.
            Cela permet d'améliorer la performance des modèles en fournissant des variables plus informatives et pertinentes.
        </p>
        <p>
            Étapes spécifiques appliquées :
        </p>
        <ul>
            <li><strong>Transformation des variables :</strong> Nous avons appliqué plusieurs transformations mathématiques aux variables existantes :
                <ul>
                    <li>Transformation logarithmique pour les variables à distribution asymétrique comme le coût horaire</li>
                    <li>Création de variables cycliques pour représenter le jour de la semaine et l'heure de la journée</li>
                    <li>Discrétisation de variables continues comme le temps de service en catégories significatives</li>
                </ul>
            </li>
            <li><strong>Création de variables dérivées :</strong> Nous avons généré de nouvelles variables à partir des données existantes :
                <ul>
                    <li>Ratio de productivité par coût (productivité/coût horaire)</li>
                    <li>Indice d'efficacité (satisfaction client/temps par client)</li>
                    <li>Score de polyvalence (nombre de tâches différentes qu'un employé peut effectuer)</li>
                    <li>Indicateur de pic d'affluence (binaire : 1 si période de pointe, 0 sinon)</li>
                </ul>
            </li>
            <li><strong>Encodage des variables catégorielles :</strong> Nous avons converti les variables catégorielles en variables numériques :
                <ul>
                    <li>One-hot encoding pour les variables nominales comme le poste et la tâche principale</li>
                    <li>Label encoding pour les variables ordinales comme le niveau d'expérience et le niveau de difficulté</li>
                    <li>Target encoding pour certaines variables catégorielles avec de nombreuses modalités</li>
                </ul>
            </li>
            <li><strong>Réduction de dimensionnalité :</strong> Pour gérer le grand nombre de variables créées, nous avons appliqué :
                <ul>
                    <li>Analyse en Composantes Principales (ACP) pour réduire la dimensionnalité tout en préservant la variance</li>
                    <li>Sélection de caractéristiques basée sur l'importance des variables dans les modèles préliminaires</li>
                </ul>
            </li>
        </ul>
        <p>
            Ces techniques de feature engineering ont permis d'enrichir nos données et d'améliorer significativement la performance des
            modèles prédictifs.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 11 : Importance des caractéristiques pour la prédiction de l'affluence</div>
            <div class="figure-content">
                <table class="performance-chart">
                    <tr>
                        <th class="algorithm">Caractéristique</th>
                        <th class="score">Importance relative (%)</th>
                        <th class="score">Visualisation</th>
                    </tr>
                    <tr>
                        <td class="algorithm">Jour de la semaine</td>
                        <td class="score">28.5%</td>
                        <td class="score"><div class="score-bar" style="width: 285px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Heure de la journée</td>
                        <td class="score">24.3%</td>
                        <td class="score"><div class="score-bar" style="width: 243px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Événement spécial</td>
                        <td class="score">15.7%</td>
                        <td class="score"><div class="score-bar" style="width: 157px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Conditions météo</td>
                        <td class="score">12.8%</td>
                        <td class="score"><div class="score-bar" style="width: 128px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Historique d'affluence</td>
                        <td class="score">10.2%</td>
                        <td class="score"><div class="score-bar" style="width: 102px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Saison</td>
                        <td class="score">5.3%</td>
                        <td class="score"><div class="score-bar" style="width: 53px;"></div></td>
                    </tr>
                    <tr>
                        <td class="algorithm">Autres facteurs</td>
                        <td class="score">3.2%</td>
                        <td class="score"><div class="score-bar" style="width: 32px;"></div></td>
                    </tr>
                </table>
            </div>
        </div>

        <h3>b. Application de Méthodes d'Apprentissage Supervisé</h3>
        <p>
            L'apprentissage supervisé utilise des données étiquetées pour entraîner des modèles prédictifs. Cela implique de fournir aux
            algorithmes des données d'entrée ainsi que les résultats attendus.
        </p>
        <p>
            Étapes spécifiques :
        </p>
        <ul>
            <li><strong>Sélection des algorithmes :</strong> Nous avons choisi plusieurs algorithmes d'apprentissage supervisé adaptés à nos problématiques :
                <ul>
                    <li>Régression linéaire et polynomiale pour prédire le nombre de clients par heure</li>
                    <li>Random Forest pour prédire la productivité des employés selon leur profil et les tâches assignées</li>
                    <li>Gradient Boosting (XGBoost) pour prédire la satisfaction client en fonction de la composition de l'équipe</li>
                    <li>Support Vector Machines (SVM) pour classifier les périodes selon leur niveau d'affluence</li>
                </ul>
            </li>
            <li><strong>Entraînement des modèles :</strong> Nous avons utilisé les données d'entraînement (70% du jeu de données) pour apprendre les relations entre les caractéristiques d'entrée et les sorties cibles.</li>
            <li><strong>Validation croisée :</strong> Nous avons appliqué une validation croisée à 5 plis pour évaluer la robustesse des modèles et prévenir le surapprentissage.</li>
            <li><strong>Évaluation des performances :</strong> Nous avons utilisé diverses métriques selon le type de problème :
                <ul>
                    <li>RMSE (Root Mean Square Error) et MAE (Mean Absolute Error) pour les problèmes de régression</li>
                    <li>Précision, Rappel, F1-score pour les problèmes de classification</li>
                    <li>AUC-ROC pour les problèmes de classification binaire</li>
                </ul>
            </li>
        </ul>
        <p>
            Les résultats de cette phase ont montré que les modèles de Gradient Boosting (XGBoost) offraient les meilleures performances
            pour la prédiction de l'affluence, avec un RMSE de 3.2 clients par heure et un R² de 0.87.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 12 : Comparaison des performances des différents algorithmes de prédiction</div>
            <div class="figure-content">
                <div style="margin-bottom: 20px;">
                    <h4 style="text-align: center;">Prédiction de l'affluence (RMSE - plus bas est meilleur)</h4>
                    <table class="performance-chart">
                        <tr>
                            <th class="algorithm">Algorithme</th>
                            <th class="score">RMSE</th>
                            <th class="score">Visualisation (plus court = meilleur)</th>
                        </tr>
                        <tr>
                            <td class="algorithm">Régression linéaire</td>
                            <td class="score">5.8</td>
                            <td class="score"><div class="score-bar" style="width: 580px; background-color: #e74c3c;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Régression polynomiale</td>
                            <td class="score">4.7</td>
                            <td class="score"><div class="score-bar" style="width: 470px; background-color: #e67e22;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Random Forest</td>
                            <td class="score">3.9</td>
                            <td class="score"><div class="score-bar" style="width: 390px; background-color: #f1c40f;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">XGBoost</td>
                            <td class="score">3.2</td>
                            <td class="score"><div class="score-bar" style="width: 320px; background-color: #2ecc71;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">SVM</td>
                            <td class="score">4.2</td>
                            <td class="score"><div class="score-bar" style="width: 420px; background-color: #e67e22;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Réseau de neurones</td>
                            <td class="score">3.5</td>
                            <td class="score"><div class="score-bar" style="width: 350px; background-color: #3498db;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Ensemble (stacking)</td>
                            <td class="score">3.0</td>
                            <td class="score"><div class="score-bar" style="width: 300px; background-color: #27ae60;"></div></td>
                        </tr>
                    </table>
                </div>

                <div>
                    <h4 style="text-align: center;">Prédiction des affectations optimales (Précision - plus haut est meilleur)</h4>
                    <table class="performance-chart">
                        <tr>
                            <th class="algorithm">Algorithme</th>
                            <th class="score">Précision</th>
                            <th class="score">Visualisation</th>
                        </tr>
                        <tr>
                            <td class="algorithm">Régression logistique</td>
                            <td class="score">72%</td>
                            <td class="score"><div class="score-bar" style="width: 72%; background-color: #f1c40f;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Random Forest</td>
                            <td class="score">89%</td>
                            <td class="score"><div class="score-bar" style="width: 89%; background-color: #27ae60;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">XGBoost</td>
                            <td class="score">87%</td>
                            <td class="score"><div class="score-bar" style="width: 87%; background-color: #2ecc71;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">SVM</td>
                            <td class="score">81%</td>
                            <td class="score"><div class="score-bar" style="width: 81%; background-color: #3498db;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">K-Nearest Neighbors</td>
                            <td class="score">76%</td>
                            <td class="score"><div class="score-bar" style="width: 76%; background-color: #9b59b6;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Réseau de neurones</td>
                            <td class="score">85%</td>
                            <td class="score"><div class="score-bar" style="width: 85%; background-color: #2ecc71;"></div></td>
                        </tr>
                        <tr>
                            <td class="algorithm">Ensemble (voting)</td>
                            <td class="score">91%</td>
                            <td class="score"><div class="score-bar" style="width: 91%; background-color: #16a085;"></div></td>
                        </tr>
                    </table>
                </div>
            </div>
        </div>

        <h3>c. Application de Méthodes d'Apprentissage Non Supervisé</h3>
        <p>
            L'apprentissage non supervisé utilise des données non étiquetées pour découvrir des structures cachées ou des regroupements
            dans les données. Cela permet d'identifier des motifs ou des anomalies sans connaître les résultats attendus.
        </p>
        <p>
            Étapes spécifiques :
        </p>
        <ul>
            <li><strong>Sélection des algorithmes :</strong> Nous avons choisi plusieurs algorithmes d'apprentissage non supervisé :
                <ul>
                    <li>K-means pour identifier des groupes d'employés aux profils similaires</li>
                    <li>DBSCAN pour détecter des patterns d'affluence atypiques</li>
                    <li>Analyse en Composantes Principales (ACP) pour visualiser les relations entre les variables</li>
                    <li>Isolation Forest pour la détection d'anomalies dans les performances des employés</li>
                </ul>
            </li>
            <li><strong>Entraînement des modèles :</strong> Nous avons appliqué ces algorithmes à nos données pour identifier des structures latentes et des regroupements naturels.</li>
            <li><strong>Évaluation des clusters :</strong> Nous avons utilisé plusieurs indices de validation :
                <ul>
                    <li>Score de silhouette pour évaluer la cohésion et la séparation des clusters</li>
                    <li>Indice de Davies-Bouldin pour mesurer la similarité entre les clusters</li>
                    <li>Inertie pour évaluer la compacité des clusters</li>
                </ul>
            </li>
            <li><strong>Interprétation des résultats :</strong> Nous avons analysé les clusters identifiés pour extraire des insights pertinents :
                <ul>
                    <li>Identification de 4 profils distincts d'employés selon leurs performances et compétences</li>
                    <li>Découverte de 3 patterns d'affluence typiques selon le jour et l'heure</li>
                    <li>Détection de combinaisons poste-tâche particulièrement efficientes ou problématiques</li>
                </ul>
            </li>
        </ul>
        <p>
            Ces analyses non supervisées ont fourni des insights précieux pour optimiser l'affectation des employés et la planification des horaires.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 13 : Visualisation des clusters d'employés identifiés par l'analyse non supervisée</div>
            <div class="figure-content">
                <div class="cluster-viz">
                    <!-- Représentation simplifiée des clusters -->
                    <div class="cluster cluster-1" style="top: 100px; left: 150px;"></div>
                    <div class="cluster cluster-1" style="top: 110px; left: 160px;"></div>
                    <div class="cluster cluster-1" style="top: 90px; left: 155px;"></div>
                    <div class="cluster cluster-1" style="top: 105px; left: 145px;"></div>
                    <div class="cluster cluster-1" style="top: 115px; left: 150px;"></div>
                    <div class="cluster cluster-1" style="top: 95px; left: 165px;"></div>
                    <div class="cluster cluster-1" style="top: 85px; left: 155px;"></div>
                    <div class="cluster cluster-1" style="top: 105px; left: 170px;"></div>

                    <div class="cluster cluster-2" style="top: 250px; left: 350px;"></div>
                    <div class="cluster cluster-2" style="top: 260px; left: 360px;"></div>
                    <div class="cluster cluster-2" style="top: 240px; left: 355px;"></div>
                    <div class="cluster cluster-2" style="top: 255px; left: 345px;"></div>
                    <div class="cluster cluster-2" style="top: 265px; left: 350px;"></div>
                    <div class="cluster cluster-2" style="top: 245px; left: 365px;"></div>
                    <div class="cluster cluster-2" style="top: 235px; left: 355px;"></div>
                    <div class="cluster cluster-2" style="top: 255px; left: 370px;"></div>

                    <div class="cluster cluster-3" style="top: 150px; left: 450px;"></div>
                    <div class="cluster cluster-3" style="top: 160px; left: 460px;"></div>
                    <div class="cluster cluster-3" style="top: 140px; left: 455px;"></div>
                    <div class="cluster cluster-3" style="top: 155px; left: 445px;"></div>
                    <div class="cluster cluster-3" style="top: 165px; left: 450px;"></div>
                    <div class="cluster cluster-3" style="top: 145px; left: 465px;"></div>
                    <div class="cluster cluster-3" style="top: 135px; left: 455px;"></div>
                    <div class="cluster cluster-3" style="top: 155px; left: 470px;"></div>

                    <div class="cluster cluster-4" style="top: 300px; left: 150px;"></div>
                    <div class="cluster cluster-4" style="top: 310px; left: 160px;"></div>
                    <div class="cluster cluster-4" style="top: 290px; left: 155px;"></div>
                    <div class="cluster cluster-4" style="top: 305px; left: 145px;"></div>
                    <div class="cluster cluster-4" style="top: 315px; left: 150px;"></div>
                    <div class="cluster cluster-4" style="top: 295px; left: 165px;"></div>
                    <div class="cluster cluster-4" style="top: 285px; left: 155px;"></div>
                    <div class="cluster cluster-4" style="top: 305px; left: 170px;"></div>

                    <!-- Légende -->
                    <div style="position: absolute; bottom: 10px; left: 10px; text-align: left;">
                        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></span> Cluster 1: Employés haute performance</div>
                        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></span> Cluster 2: Employés en formation</div>
                        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: #2ecc71; border-radius: 50%;"></span> Cluster 3: Employés polyvalents</div>
                        <div><span style="display: inline-block; width: 10px; height: 10px; background-color: #f39c12; border-radius: 50%;"></span> Cluster 4: Employés spécialisés</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>III.8 Optimisation</h2>
        <p>
            L'optimisation des modèles est une étape cruciale dans le développement des modèles prédictifs. Son objectif est d'améliorer
            la performance des modèles en ajustant leurs paramètres et en combinant plusieurs modèles pour obtenir des prédictions plus
            précises et robustes.
        </p>

        <h3>a. Optimisation des hyperparamètres</h3>
        <p>
            Pour chaque algorithme utilisé, nous avons procédé à une optimisation rigoureuse des hyperparamètres :
        </p>
        <ul>
            <li><strong>Méthodes d'optimisation utilisées :</strong>
                <ul>
                    <li>Grid Search : Exploration systématique d'une grille de valeurs possibles pour les hyperparamètres</li>
                    <li>Random Search : Exploration aléatoire de l'espace des hyperparamètres, plus efficace pour les espaces de grande dimension</li>
                    <li>Optimisation bayésienne : Approche plus sophistiquée utilisant un modèle probabiliste pour guider la recherche des hyperparamètres optimaux</li>
                </ul>
            </li>
            <li><strong>Hyperparamètres optimisés pour les principaux algorithmes :</strong>
                <ul>
                    <li>Random Forest : nombre d'arbres, profondeur maximale, nombre minimal d'échantillons par feuille</li>
                    <li>XGBoost : taux d'apprentissage, profondeur maximale, gamma, lambda</li>
                    <li>SVM : paramètre de régularisation C, paramètre du noyau gamma, type de noyau</li>
                    <li>K-means : nombre de clusters, méthode d'initialisation, nombre maximal d'itérations</li>
                </ul>
            </li>
        </ul>
        <p>
            Cette optimisation a permis d'améliorer significativement les performances des modèles. Par exemple, pour le modèle XGBoost
            de prédiction d'affluence, l'optimisation des hyperparamètres a réduit le RMSE de 4.1 à 3.2 clients par heure.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 14 : Impact de l'optimisation des hyperparamètres sur la performance des modèles</div>
            <div class="figure-content">
                <table class="performance-chart">
                    <tr>
                        <th class="algorithm">Algorithme</th>
                        <th class="score">Métrique</th>
                        <th class="score">Avant optimisation</th>
                        <th class="score">Après optimisation</th>
                        <th class="score">Amélioration</th>
                    </tr>
                    <tr>
                        <td class="algorithm">XGBoost</td>
                        <td class="score">RMSE</td>
                        <td class="score">4.1</td>
                        <td class="score">3.2</td>
                        <td class="score" style="color: #27ae60;">22%</td>
                    </tr>
                    <tr>
                        <td class="algorithm">Random Forest</td>
                        <td class="score">Précision</td>
                        <td class="score">82%</td>
                        <td class="score">89%</td>
                        <td class="score" style="color: #27ae60;">8.5%</td>
                    </tr>
                    <tr>
                        <td class="algorithm">SVM</td>
                        <td class="score">F1-score</td>
                        <td class="score">0.75</td>
                        <td class="score">0.83</td>
                        <td class="score" style="color: #27ae60;">10.7%</td>
                    </tr>
                    <tr>
                        <td class="algorithm">K-means</td>
                        <td class="score">Silhouette</td>
                        <td class="score">0.62</td>
                        <td class="score">0.78</td>
                        <td class="score" style="color: #27ae60;">25.8%</td>
                    </tr>
                    <tr>
                        <td class="algorithm">Réseau de neurones</td>
                        <td class="score">RMSE</td>
                        <td class="score">3.9</td>
                        <td class="score">3.5</td>
                        <td class="score" style="color: #27ae60;">10.3%</td>
                    </tr>
                </table>
            </div>
        </div>

        <h3>b. Ensembling et stacking</h3>
        <p>
            Pour améliorer davantage la robustesse et la précision des prédictions, nous avons appliqué des techniques d'ensembling et de stacking :
        </p>
        <ul>
            <li><strong>Méthodes d'ensembling utilisées :</strong>
                <ul>
                    <li>Bagging : Entraînement de plusieurs modèles sur différents sous-ensembles des données et agrégation des prédictions</li>
                    <li>Boosting : Entraînement séquentiel de modèles, chacun se concentrant sur les erreurs des précédents</li>
                    <li>Voting : Combinaison des prédictions de différents modèles par vote majoritaire (classification) ou moyenne (régression)</li>
                </ul>
            </li>
            <li><strong>Stacking :</strong> Nous avons développé une architecture à deux niveaux :
                <ul>
                    <li>Premier niveau : Entraînement de modèles de base diversifiés (Random Forest, XGBoost, SVM, réseaux de neurones)</li>
                    <li>Second niveau : Utilisation d'un méta-modèle (régression logistique régularisée) pour combiner les prédictions des modèles de base</li>
                </ul>
            </li>
        </ul>
        <p>
            Cette approche d'ensembling a permis d'améliorer la précision des prédictions d'affluence de 87% à 92% et de réduire la variance
            des prédictions, rendant le système plus robuste face aux données nouvelles ou atypiques.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 15 : Architecture du modèle d'ensemble pour la prédiction des besoins en personnel</div>
            <div class="figure-content" style="text-align: center;">
                <!-- Niveau 1 : Modèles de base -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 10px; background-color: #3498db; color: white; border-radius: 5px;">Random Forest</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 10px; background-color: #2ecc71; color: white; border-radius: 5px;">XGBoost</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 10px; background-color: #e74c3c; color: white; border-radius: 5px;">SVM</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 10px; background-color: #9b59b6; color: white; border-radius: 5px;">Réseau de neurones</div>
                </div>

                <!-- Flèches -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                </div>

                <!-- Prédictions des modèles de base -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 5px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">Prédiction 1</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 5px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">Prédiction 2</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 5px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">Prédiction 3</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%; padding: 5px; background-color: #f9f9f9; border: 1px solid #ddd; border-radius: 5px;">Prédiction 4</div>
                </div>

                <!-- Flèches convergentes -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↘</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↓</div>
                    <div style="display: inline-block; width: 20%; margin: 0 2%;">↙</div>
                </div>

                <!-- Niveau 2 : Méta-modèle -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block; width: 50%; padding: 10px; background-color: #f39c12; color: white; border-radius: 5px;">Méta-modèle (Régression logistique régularisée)</div>
                </div>

                <!-- Flèche finale -->
                <div style="margin-bottom: 20px;">
                    <div style="display: inline-block;">↓</div>
                </div>

                <!-- Prédiction finale -->
                <div>
                    <div style="display: inline-block; width: 50%; padding: 10px; background-color: #16a085; color: white; border-radius: 5px;">Prédiction finale</div>
                </div>
            </div>
        </div>

        <h3>c. Validation et tests finaux</h3>
        <p>
            Après l'optimisation, nous avons procédé à une validation rigoureuse des modèles finaux :
        </p>
        <ul>
            <li><strong>Validation sur données historiques :</strong> Test des modèles sur des données historiques non utilisées pendant l'entraînement pour évaluer leur capacité de généralisation.</li>
            <li><strong>Tests en conditions réelles :</strong> Déploiement des modèles en parallèle du système existant pendant une période de 2 semaines pour comparer les prédictions aux observations réelles.</li>
            <li><strong>Analyse de sensibilité :</strong> Évaluation de la robustesse des modèles face à des variations dans les données d'entrée pour identifier les points de fragilité potentiels.</li>
        </ul>
        <p>
            Les résultats de ces tests ont confirmé la supériorité des modèles optimisés, avec une amélioration moyenne de 15% de la précision
            des prédictions par rapport aux méthodes traditionnelles basées sur des règles.
        </p>

        <div class="figure">
            <div class="figure-title">Figure 16 : Prédiction de l'affluence et besoins en personnel correspondants pour une semaine type</div>
            <div class="figure-content">
                <div class="chart">
                    <div class="chart-y-axis"></div>
                    <div class="chart-axis"></div>

                    <!-- Représentation simplifiée d'un graphique de prédiction d'affluence -->
                    <!-- Lundi -->
                    <div class="chart-bar" style="height: 100px; left: 50px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 150px; left: 80px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 200px; left: 110px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 120px; left: 140px; background-color: #3498db;"></div>

                    <!-- Mardi -->
                    <div class="chart-bar" style="height: 90px; left: 190px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 180px; left: 220px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 220px; left: 250px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 130px; left: 280px; background-color: #3498db;"></div>

                    <!-- Mercredi -->
                    <div class="chart-bar" style="height: 110px; left: 330px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 190px; left: 360px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 230px; left: 390px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 140px; left: 420px; background-color: #3498db;"></div>

                    <!-- Jeudi -->
                    <div class="chart-bar" style="height: 100px; left: 470px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 170px; left: 500px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 210px; left: 530px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 120px; left: 560px; background-color: #3498db;"></div>

                    <!-- Vendredi -->
                    <div class="chart-bar" style="height: 130px; left: 610px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 200px; left: 640px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 250px; left: 670px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 180px; left: 700px; background-color: #3498db;"></div>

                    <!-- Samedi -->
                    <div class="chart-bar" style="height: 180px; left: 750px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 250px; left: 780px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 280px; left: 810px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 220px; left: 840px; background-color: #3498db;"></div>

                    <!-- Dimanche -->
                    <div class="chart-bar" style="height: 150px; left: 890px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 220px; left: 920px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 190px; left: 950px; background-color: #3498db;"></div>
                    <div class="chart-bar" style="height: 130px; left: 980px; background-color: #3498db;"></div>

                    <!-- Ligne de besoins en personnel -->
                    <div class="chart-line" style="height: 100px; left: 50px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 150px; left: 190px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 160px; left: 330px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 140px; left: 470px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 170px; left: 610px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 200px; left: 750px; width: 110px; background-color: #e74c3c;"></div>
                    <div class="chart-line" style="height: 160px; left: 890px; width: 110px; background-color: #e74c3c;"></div>

                    <!-- Légende -->
                    <div style="position: absolute; top: 10px; left: 10px; text-align: left;">
                        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: #3498db;"></span> Affluence prévue (clients/heure)</div>
                        <div><span style="display: inline-block; width: 10px; height: 2px; background-color: #e74c3c;"></span> Besoins en personnel</div>
                    </div>

                    <!-- Jours de la semaine -->
                    <div style="position: absolute; bottom: 10px; left: 95px; transform: translateX(-50%);">Lundi</div>
                    <div style="position: absolute; bottom: 10px; left: 235px; transform: translateX(-50%);">Mardi</div>
                    <div style="position: absolute; bottom: 10px; left: 375px; transform: translateX(-50%);">Mercredi</div>
                    <div style="position: absolute; bottom: 10px; left: 515px; transform: translateX(-50%);">Jeudi</div>
                    <div style="position: absolute; bottom: 10px; left: 655px; transform: translateX(-50%);">Vendredi</div>
                    <div style="position: absolute; bottom: 10px; left: 795px; transform: translateX(-50%);">Samedi</div>
                    <div style="position: absolute; bottom: 10px; left: 935px; transform: translateX(-50%);">Dimanche</div>
                </div>
            </div>
        </div>
    </div>

    <div class="section">
        <h2>III.9 Résultats et impact</h2>
        <p>
            Les premiers résultats de l'implémentation de notre système de prédiction des besoins en ressources humaines sont très
            encourageants. Après trois mois d'utilisation, nous avons observé des améliorations significatives dans plusieurs domaines clés :
        </p>

        <div class="figure">
            <div class="figure-title">Tableau 6 : Résumé des résultats obtenus après trois mois d'utilisation du système</div>
            <div class="figure-content">
                <table class="results-table">
                    <tr>
                        <th>Indicateur</th>
                        <th>Avant implémentation</th>
                        <th>Après 3 mois</th>
                        <th>Amélioration</th>
                    </tr>
                    <tr>
                        <td>Coûts de personnel</td>
                        <td>100% (référence)</td>
                        <td>85%</td>
                        <td class="improvement">-15%</td>
                    </tr>
                    <tr>
                        <td>Satisfaction client (score sur 100)</td>
                        <td>78</td>
                        <td>87.4</td>
                        <td class="improvement">+12%</td>
                    </tr>
                    <tr>
                        <td>Temps consacré à la planification (heures/semaine)</td>
                        <td>10</td>
                        <td>8</td>
                        <td class="improvement">-20%</td>
                    </tr>
                    <tr>
                        <td>Situations de sous-effectif (occurrences/mois)</td>
                        <td>24</td>
                        <td>18</td>
                        <td class="improvement">-25%</td>
                    </tr>
                    <tr>
                        <td>Productivité globale des employés (indice)</td>
                        <td>100 (référence)</td>
                        <td>118</td>
                        <td class="improvement">+18%</td>
                    </tr>
                    <tr>
                        <td>Taux d'erreur moyen</td>
                        <td>5.8%</td>
                        <td>3.9%</td>
                        <td class="improvement">-32.8%</td>
                    </tr>
                    <tr>
                        <td>Temps d'attente moyen des clients (minutes)</td>
                        <td>4.5</td>
                        <td>3.2</td>
                        <td class="improvement">-28.9%</td>
                    </tr>
                    <tr>
                        <td>Rotation du personnel (taux annualisé)</td>
                        <td>35%</td>
                        <td>28%</td>
                        <td class="improvement">-20%</td>
                    </tr>
                </table>
            </div>
        </div>

        <p>
            Ces résultats démontrent la valeur ajoutée significative de l'approche basée sur les données et l'apprentissage automatique
            pour la gestion des ressources humaines dans un environnement de café. Les améliorations les plus notables sont :
        </p>
        <ul>
            <li><strong>Réduction des coûts de personnel de 15%</strong> grâce à une meilleure planification des horaires et une allocation plus efficace des ressources.</li>
            <li><strong>Augmentation de la satisfaction client de 12%</strong>, mesurée par des enquêtes régulières et le taux de retour des clients.</li>
            <li><strong>Diminution de 20% du temps consacré à la planification des horaires</strong> par les gestionnaires, leur permettant de se concentrer sur d'autres aspects de la gestion du café.</li>
            <li><strong>Réduction de 25% des situations de sous-effectif</strong> pendant les périodes de pointe, améliorant ainsi la qualité du service et réduisant le stress des employés.</li>
            <li><strong>Amélioration de 18% de la productivité globale des employés</strong> grâce à une meilleure adéquation entre leurs compétences et les tâches assignées.</li>
        </ul>

        <div class="figure">
            <div class="figure-title">Figure 17 : Impact de l'optimisation des affectations sur la satisfaction client et les coûts</div>
            <div class="figure-content">
                <div style="width: 100%; height: 400px; position: relative; background-color: #f8f9fa; border: 1px solid #ddd; border-radius: 5px;">
                    <!-- Axe Y gauche (Satisfaction client) -->
                    <div style="position: absolute; top: 0; left: 40px; height: 100%; width: 1px; background-color: #333;"></div>
                    <div style="position: absolute; top: 350px; left: 10px; transform: rotate(-90deg); transform-origin: left top;">Satisfaction client</div>

                    <!-- Axe Y droit (Coûts) -->
                    <div style="position: absolute; top: 0; right: 40px; height: 100%; width: 1px; background-color: #333;"></div>
                    <div style="position: absolute; top: 350px; right: 10px; transform: rotate(90deg); transform-origin: right top;">Coûts de personnel</div>

                    <!-- Axe X (Mois) -->
                    <div style="position: absolute; bottom: 40px; left: 40px; width: calc(100% - 80px); height: 1px; background-color: #333;"></div>
                    <div style="position: absolute; bottom: 10px; left: 50%; transform: translateX(-50%);">Période (mois)</div>

                    <!-- Points de données - Satisfaction client (ligne bleue) -->
                    <div style="position: absolute; bottom: 100px; left: 80px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 110px; left: 160px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 130px; left: 240px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 150px; left: 320px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 180px; left: 400px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 200px; left: 480px; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></div>

                    <!-- Ligne de tendance - Satisfaction client -->
                    <div style="position: absolute; bottom: 100px; left: 80px; width: 400px; height: 2px; background-color: #3498db; transform-origin: left bottom; transform: rotate(14deg);"></div>

                    <!-- Points de données - Coûts (ligne rouge) -->
                    <div style="position: absolute; bottom: 200px; left: 80px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 190px; left: 160px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 170px; left: 240px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 150px; left: 320px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 130px; left: 400px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>
                    <div style="position: absolute; bottom: 120px; left: 480px; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></div>

                    <!-- Ligne de tendance - Coûts -->
                    <div style="position: absolute; bottom: 200px; left: 80px; width: 400px; height: 2px; background-color: #e74c3c; transform-origin: left bottom; transform: rotate(-11deg);"></div>

                    <!-- Étiquettes des mois -->
                    <div style="position: absolute; bottom: 20px; left: 80px;">Mois -3</div>
                    <div style="position: absolute; bottom: 20px; left: 160px;">Mois -2</div>
                    <div style="position: absolute; bottom: 20px; left: 240px;">Mois -1</div>
                    <div style="position: absolute; bottom: 20px; left: 320px;">Mois 1</div>
                    <div style="position: absolute; bottom: 20px; left: 400px;">Mois 2</div>
                    <div style="position: absolute; bottom: 20px; left: 480px;">Mois 3</div>

                    <!-- Ligne verticale d'implémentation -->
                    <div style="position: absolute; top: 40px; bottom: 40px; left: 280px; width: 1px; background-color: #333; border-left: 1px dashed #333;"></div>
                    <div style="position: absolute; top: 50px; left: 290px; font-style: italic;">Implémentation du système</div>

                    <!-- Légende -->
                    <div style="position: absolute; top: 10px; left: 10px; text-align: left;">
                        <div style="margin-bottom: 5px;"><span style="display: inline-block; width: 10px; height: 10px; background-color: #3498db; border-radius: 50%;"></span> Satisfaction client</div>
                        <div><span style="display: inline-block; width: 10px; height: 10px; background-color: #e74c3c; border-radius: 50%;"></span> Coûts de personnel</div>
                    </div>
                </div>
            </div>
        </div>

        <p>
            L'analyse détaillée des résultats montre également des impacts positifs sur d'autres aspects de la gestion du café :
        </p>
        <ul>
            <li><strong>Amélioration de l'ambiance de travail</strong> : La réduction des situations de stress liées au sous-effectif et la meilleure adéquation entre les compétences et les tâches ont contribué à améliorer la satisfaction des employés.</li>
            <li><strong>Réduction de la rotation du personnel</strong> : Le taux de rotation annualisé est passé de 35% à 28%, ce qui représente une économie significative en termes de coûts de recrutement et de formation.</li>
            <li><strong>Meilleure gestion des pics d'activité</strong> : Le système a permis d'anticiper avec précision les périodes de forte affluence et d'ajuster les ressources en conséquence, réduisant ainsi le temps d'attente moyen des clients de 4.5 à 3.2 minutes.</li>
            <li><strong>Optimisation de la formation</strong> : L'identification des lacunes en compétences a permis de cibler les besoins en formation, améliorant ainsi l'efficacité des programmes de développement des employés.</li>
        </ul>
        <p>
            Ces résultats démontrent que l'approche basée sur les données et l'apprentissage automatique peut apporter des bénéfices
            tangibles dans la gestion des ressources humaines, même dans un secteur traditionnel comme la restauration. Le retour sur
            investissement du projet est estimé à moins de six mois, compte tenu des économies réalisées et de l'augmentation du chiffre
            d'affaires liée à l'amélioration de la satisfaction client.
        </p>
    </div>

    <div class="section">
        <h2>III.10 Conclusion et perspectives</h2>
        <p>
            Ce chapitre a présenté la modélisation informatique du système de prédiction des besoins en ressources humaines pour un café.
            Nous avons détaillé les objectifs en matière d'apprentissage, l'analyse des risques, les actions orientées science des données,
            la préparation et le traitement des données, l'optimisation des modèles, et l'implémentation du système.
        </p>
        <p>
            Notre approche méthodique a permis de développer un système robuste et efficace qui répond aux défis spécifiques de la gestion
            des ressources humaines dans un environnement de café. En identifiant les paramètres critiques, en établissant des seuils de
            variation pertinents, et en définissant des règles spécifiques basées sur l'analyse des données, nous avons créé un outil d'aide
            à la décision qui transforme les données brutes en recommandations actionnables.
        </p>
        <p>
            Les résultats obtenus confirment la pertinence de notre approche et son potentiel pour améliorer significativement la gestion
            des ressources humaines dans le secteur de la restauration. La réduction des coûts de personnel, l'augmentation de la satisfaction
            client, et la diminution du temps consacré à la planification démontrent la valeur ajoutée tangible du système.
        </p>
        <p>
            Cependant, plusieurs perspectives d'amélioration se dessinent pour l'avenir :
        </p>
        <ul>
            <li>Intégration de données externes supplémentaires (tendances des réseaux sociaux, événements locaux, données météorologiques précises) pour améliorer encore la précision des prédictions.</li>
            <li>Développement de modèles plus sophistiqués pour capturer des patterns saisonniers complexes et des événements exceptionnels.</li>
            <li>Extension du système pour prendre en compte d'autres aspects de la gestion des ressources humaines, comme la formation et le développement des compétences, la gestion des talents, et la planification de carrière.</li>
            <li>Adaptation du système pour d'autres types d'établissements de restauration ou de service, en tenant compte de leurs spécificités opérationnelles.</li>
            <li>Implémentation d'un système d'apprentissage continu qui s'améliore automatiquement au fil du temps en intégrant les nouvelles données et les retours d'expérience.</li>
        </ul>
        <p>
            En conclusion, notre système de prédiction des besoins en ressources humaines représente une avancée significative dans
            l'application de l'intelligence artificielle à la gestion opérationnelle des cafés. Il démontre comment les techniques de
            machine learning peuvent être appliquées avec succès pour résoudre des problèmes concrets et créer de la valeur dans un
            secteur traditionnel. Cette approche basée sur les données permet non seulement d'optimiser les coûts et d'améliorer la
            satisfaction client, mais aussi de créer un environnement de travail plus équilibré et prévisible pour les employés,
            contribuant ainsi à réduire le turnover et à améliorer l'engagement du personnel.
        </p>
    </div>
</body>
</html>
