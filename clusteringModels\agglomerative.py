from sklearn.cluster import AgglomerativeClustering
from sklearn.metrics import silhouette_score
import matplotlib.pyplot as plt
import pandas as pd
import numpy as np
import sklearn
from itertools import product

class Agglomerative_Clustering:
    def __init__(self, data, max_clusters=10):
        self.data = data
        self.max_clusters = max_clusters
        self.best_k = None
        self.best_linkage = None
        self.best_affinity = None
        self.best_score = -1
        self.model = None
        self.labels = None
        # Détection automatique de la version de scikit-learn
        self.use_metric = self._use_metric_instead_of_affinity()

    def _use_metric_instead_of_affinity(self):
        version = sklearn.__version__.split('.')
        major, minor = int(version[0]), int(version[1])
        return (major == 1 and minor >= 2) or (major > 1)

    def _find_best_params(self):
        print("Recherche des meilleurs hyperparamètres...")
        candidate_linkages = ['ward', 'average', 'complete', 'single']
        candidate_affinities = ['euclidean', 'manhattan', 'cosine']
        silhouette_scores = []

        for k in range(2, self.max_clusters + 1):
            for linkage in candidate_linkages:
                affinities = ['euclidean'] if linkage == 'ward' else candidate_affinities

                for affinity in affinities:
                    try:
                        if self.use_metric:
                            model = AgglomerativeClustering(n_clusters=k, linkage=linkage, metric=affinity)
                        else:
                            model = AgglomerativeClustering(n_clusters=k, linkage=linkage, affinity=affinity)

                        labels = model.fit_predict(self.data)
                        score = silhouette_score(self.data, labels)
                        silhouette_scores.append((k, linkage, affinity, score))

                        if score > self.best_score:
                            self.best_score = score
                            self.best_k = k
                            self.best_linkage = linkage
                            self.best_affinity = affinity
                    except Exception as e:
                        print(f"Erreur avec k={k}, linkage={linkage}, affinity/metric={affinity} : {e}")
                        continue

        print(f"✔ Meilleurs paramètres trouvés : k = {self.best_k}, linkage = {self.best_linkage}, "
              f"{'metric' if self.use_metric else 'affinity'} = {self.best_affinity}, "
              f"silhouette = {self.best_score:.4f}")

    def _fit_model(self):
        if self.use_metric:
            self.model = AgglomerativeClustering(
                n_clusters=self.best_k,
                linkage=self.best_linkage,
                metric=self.best_affinity
            )
        else:
            self.model = AgglomerativeClustering(
                n_clusters=self.best_k,
                linkage=self.best_linkage,
                affinity=self.best_affinity
            )
        self.labels = self.model.fit_predict(self.data)

    def _describe_clusters(self):
        print("\nDescription des clusters :")
        for label in range(self.best_k):
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def _visualize(self):
        dims = self.data.shape[1]
        data = self.data.values if isinstance(self.data, pd.DataFrame) else self.data
        labels = self.labels

        if dims == 2:
            plt.scatter(data[:, 0], data[:, 1], c=labels, cmap='tab10', s=40)
            plt.title("Clustering Agglomératif (2D)")
            plt.xlabel("X1")
            plt.ylabel("X2")
            plt.grid(True)
            plt.show()

        elif dims == 3:
            from mpl_toolkits.mplot3d import Axes3D
            fig = plt.figure()
            ax = fig.add_subplot(111, projection='3d')
            ax.scatter(data[:, 0], data[:, 1], data[:, 2], c=labels, cmap='tab10', s=40)
            ax.set_title("Clustering Agglomératif (3D)")
            ax.set_xlabel("X1")
            ax.set_ylabel("X2")
            ax.set_zlabel("X3")
            plt.show()
        else:
            print("Visualisation uniquement disponible pour 2D ou 3D.")

    def run_clustering_hiérarchique_agglomératif_clustering(self):
        self._find_best_params()
        self._fit_model()
        self._describe_clusters()
        self._visualize()
