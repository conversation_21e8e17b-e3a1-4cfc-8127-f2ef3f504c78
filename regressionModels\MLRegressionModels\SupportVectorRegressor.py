import numpy as np
import random
from sklearn.svm import SVR
from sklearn.model_selection import cross_val_score
from evaluationModels.evaluation_regressor import RegressionEvaluator

class Method_SVR_Regressor:
    def __init__(self):
        self.best_svr = None
        self.best_params = None
        self.best_score = float('inf')

    def _random_sample(self):
        """
        Génère un jeu de paramètres aléatoire valide pour SVR.
        """
        C = np.random.uniform(0.1, 10)
        epsilon = np.random.uniform(0.01, 1)
        kernel = random.choice(['linear', 'rbf', 'poly'])
        gamma = random.choice(['scale', 'auto'])

        if kernel == 'poly':
            degree = random.choice([2, 3, 4])
            return {'C': C, 'epsilon': epsilon, 'kernel': kernel, 'gamma': gamma, 'degree': degree}
        else:
            return {'C': C, 'epsilon': epsilon, 'kernel': kernel, 'gamma': gamma}

    def train_svr(self, X_train, y_train, n_iter=50, cv=5, random_state=42):
        """
        Recherche des meilleurs hyperparamètres avec une logique conditionnelle.
        """
        print("_________________Entraînement du modèle SVR pour la régression_________________")
        print("Recherche personnalisée des hyperparamètres SVR...")
        np.random.seed(random_state)
        random.seed(random_state)

        for i in range(n_iter):
            params = self._random_sample()
            model = SVR(**params)
            try:
                # Cross-validation avec scoring = MSE
                score = -np.mean(cross_val_score(model, X_train, y_train, cv=cv, scoring='neg_mean_squared_error'))

                if score < self.best_score:
                    self.best_score = score
                    self.best_params = params
                    self.best_svr = model

            except Exception as e:
                print(f"Échec pour paramètres {params} : {e}")
                continue

        print(f"\nMeilleurs paramètres : {self.best_params}")
        print(f"Meilleur MSE (val. croisée) : {self.best_score:.4f}")

        self.best_svr.fit(X_train, y_train)
        return self

    def predict(self, X_test):
        if self.best_svr is None:
            raise ValueError(" Le modèle SVR n'a pas été entraîné.")
        print("Prédiction avec les données de test...")
        return self.best_svr.predict(X_test)

    def run_svr_regressor(self, X_train, y_train, X_test, y_test):
        """
        Entraîne le modèle, effectue la prédiction et lance l'évaluation.
        """
        self.train_svr(X_train, y_train)
        y_pred = self.predict(X_test)

        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
