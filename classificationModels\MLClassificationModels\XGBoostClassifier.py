from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from xgboost import XGBClassifier
from scipy.stats import randint as sp_randint, uniform as sp_uniform
from evaluationModels.evaluation_classification import ClassifierEvaluator


class Method_XGBoost_Classifier:
    # def __init__(self):
    #     self.best_parameter = None

    # def train_xgboost(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
    #     """
    #     Entraîne le modèle XGBoost avec RandomizedSearchCV
    #     """
    #     print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

    #     # param_dist = {
    #     #     'max_depth': sp_randint(2, 6),
    #     #     'n_estimators': sp_randint(50, 200),
    #     #     'learning_rate': sp_uniform(0.01, 0.3)
    #     # }

    #     param_dist = {
    #         'n_estimators': sp_randint(50, 200),
    #         'max_depth': sp_randint(3, 10),
    #         'learning_rate': sp_uniform(0.01, 0.3),
    #         'subsample': sp_uniform(0.7, 0.3),
    #         'colsample_bytree': sp_uniform(0.6, 0.4),
    #         'gamma': sp_uniform(0, 5)
    #     }

    #     model = XGBClassifier(
    #         use_label_encoder=False,
    #         eval_metric='mlogloss',
    #         verbosity=0,
    #         random_state=random_state,
    #         tree_method='auto',  # ou 'hist'
    #         device='cpu'         # force l'utilisation du CPU
    #     )

    #     random_search = RandomizedSearchCV(
    #         model,
    #         param_distributions=param_dist,
    #         n_iter=n_iter,
    #         scoring=make_scorer(accuracy_score),
    #         cv=cv,
    #         random_state=random_state,
    #         n_jobs=-1,
    #         verbose=1
    #     )

    #     random_search.fit(X_train, y_train)
    #     self.best_parameter = random_search.best_estimator_

    #     print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
    #     print(f"Score de validation croisée : {random_search.best_score_:.4f}")
    #     return self

    # def predict(self, X_test):
    #     """
    #     Prédit les classes avec le meilleur modèle entraîné
    #     """
    #     if self.best_parameter is None:
    #         raise ValueError("Le modèle n'a pas été entraîné.")
    #     print("Prédiction avec le modèle optimal...")
    #     return self.best_parameter.predict(X_test)

    # def run_xgboost_classifier(self, X_train, y_train, X_test, y_test):
    #     """
    #     Fonction complète : entraînement + prédiction + évaluation
    #     """
    #     print("______________Entraînement du modèle XGBoost______________")
    #     self.train_xgboost(X_train, y_train)

    #     y_pred = self.predict(X_test)

    #     print("_________________Évaluation du modèle_________________")
    #     evaluator = ClassifierEvaluator(y_test, y_pred)
    #     evaluator.evaluation_metrics()

    def run_xgboost_classifier(self, X_train, y_train, X_test, y_test):
        print("______________prob CPU______________")

