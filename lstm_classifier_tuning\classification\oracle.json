{"ongoing_trials": {"tuner0": "02"}, "hyperparameters": {"space": [{"class_name": "Int", "config": {"name": "units", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "num_layers", "default": null, "conditions": [], "min_value": 1, "max_value": 3, "step": 1, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "units_0", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "dense_units", "default": null, "conditions": [], "min_value": 64, "max_value": 256, "step": 64, "sampling": "linear"}}, {"class_name": "Float", "config": {"name": "learning_rate", "default": 0.0001, "conditions": [], "min_value": 0.0001, "max_value": 0.01, "step": null, "sampling": "log"}}, {"class_name": "Int", "config": {"name": "units_1", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}, {"class_name": "Int", "config": {"name": "units_2", "default": null, "conditions": [], "min_value": 32, "max_value": 128, "step": 32, "sampling": "linear"}}], "values": {"units": 32, "num_layers": 1, "units_0": 32, "dense_units": 64, "learning_rate": 0.0001, "units_1": 32, "units_2": 32}}, "start_order": ["00", "01", "02"], "end_order": ["00", "01"], "run_times": {"00": 1, "01": 1}, "retry_queue": [], "seed": 8399, "seed_state": 8417, "tried_so_far": ["af90962ce79cd1aff440d0f5e56b5d16", "e5cf417543f03a999d768a9947982077", "14fd05e9421d25c403ddcd2568506443"], "id_to_hash": {"00": "e5cf417543f03a999d768a9947982077", "01": "14fd05e9421d25c403ddcd2568506443", "02": "af90962ce79cd1aff440d0f5e56b5d16"}, "display": {"search_start": "2025-05-16T16:17:27.350546", "trial_start": {"00": "2025-05-16T16:17:27.350546", "01": "2025-05-16T16:17:56.373847"}, "trial_number": {"00": 1, "01": 2}}}