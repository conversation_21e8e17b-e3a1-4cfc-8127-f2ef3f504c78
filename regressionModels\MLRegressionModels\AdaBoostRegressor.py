from sklearn.ensemble import <PERSON><PERSON>oostRegressor
from sklearn.tree import Decision<PERSON>reeRegressor
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, mean_squared_error, r2_score
from scipy.stats import randint, uniform

from evaluationModels.evaluation_regressor import RegressionEvaluator 

# Définition de la classe pour entraîner, prédire et évaluer un modèle AdaBoost en régression
class Method_AdaBoost_Regressor:

    def __init__(self):
        self.best_model = None

    def train_adaboost(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres pour AdaBoostRegressor...")

        # Plages d’hyperparamètres à tester
        param_dist = {
            'n_estimators': randint(50, 300),
            'learning_rate': uniform(0.01, 1.0)
        }

        base_learner = DecisionTreeRegressor(max_depth=3)

        model = AdaBoostRegressor(estimator=base_learner, random_state=random_state)

        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(mean_squared_error, greater_is_better=False),  # Attention au signe !
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)
        self.best_model = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score MSE (négatif) de validation croisée : {random_search.best_score_:.4f}")
        return self

    def predict(self, X_test):
        if self.best_model is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle AdaBoostRegressor...")
        return self.best_model.predict(X_test)

    def run_adaboost_regressor(self, X_train, y_train, X_test, y_test):
        print("__________ Entraînement du modèle AdaBoost Regressor __________")
        self.train_adaboost(X_train, y_train)

        y_pred = self.predict(X_test)

        print("__________ Évaluation du modèle AdaBoost Regressor __________")
        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
