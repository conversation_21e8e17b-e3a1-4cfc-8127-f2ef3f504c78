from sklearn.metrics import calinski_harabasz_score, davies_bouldin_score, silhouette_score
from sklearn.cluster import OPTICS
from sklearn.decomposition import PCA
import matplotlib.pyplot as plt
import numpy as np
import random
from itertools import product

class OPTICSClustering:
    def __init__(self, data):
        self.data = data
        self.best_params = None
        self.model = None
        self.labels = None

    def _optimize_hyperparameters(self, param_grid=None, n_iter=10):
        print("Recherche des meilleurs hyperparamètres...")
        if param_grid is None:
            param_grid = {
                'min_samples': [3, 5, 10],
                'xi': [0.01, 0.05, 0.1],
                'min_cluster_size': [0.05, 0.1, 0.2],
                'max_eps': [np.inf, 0.5, 1.0],
                'metric': ['euclidean', 'manhattan', 'cosine'],
                'cluster_method': ['xi'],
            }

        keys, values = zip(*param_grid.items())
        all_combinations = [dict(zip(keys, v)) for v in product(*values)]
        sampled_combinations = random.sample(all_combinations, min(n_iter, len(all_combinations)))

        best_score = -1
        best_config = None

        for params in sampled_combinations:
            try:
                model = OPTICS(**params)
                labels = model.fit_predict(self.data)

                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)

                if n_clusters > 1:
                    mask = labels != -1
                    silhouette = silhouette_score(self.data[mask], labels[mask])
                    ch_score = calinski_harabasz_score(self.data[mask], labels[mask])
                    db_score = davies_bouldin_score(self.data[mask], labels[mask])

                    print(f"Test {params} → Clusters: {n_clusters} | Silhouette: {silhouette:.4f} | CH: {ch_score:.2f} | DB: {db_score:.2f}")

                    if silhouette > best_score:
                        best_score = silhouette
                        best_config = params
            except Exception as e:
                print(f"Erreur avec {params} : {e}")

        self.best_params = best_config
        if best_config:
            print(f"\nMeilleure configuration trouvée : {best_config}")
        else:
            print("Aucune configuration optimale trouvée.")

    def _fit_model(self):
        self.model = OPTICS(**self.best_params)
        self.labels = self.model.fit_predict(self.data)

    def _describe_clusters(self):
        print("\n Description des clusters :")
        cluster_labels = set(self.labels)
        if -1 in cluster_labels:
            cluster_labels.remove(-1)
        for label in cluster_labels:
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")
        noise = np.sum(self.labels == -1)
        if noise > 0:
            print(f"Bruit (noise) : {noise} points")

    def _evaluate_performance(self):
        print("\nÉvaluation du clustering :")
        if len(set(self.labels)) > 1:
            mask = self.labels != -1
            if len(set(self.labels[mask])) > 1:
                silhouette = silhouette_score(self.data[mask], self.labels[mask])
                ch_score = calinski_harabasz_score(self.data[mask], self.labels[mask])
                db_score = davies_bouldin_score(self.data[mask], self.labels[mask])
                print(f"✔ Silhouette Score         : {silhouette:.4f}")
                print(f"✔ Calinski-Harabasz Score : {ch_score:.2f}")
                print(f"✔ Davies-Bouldin Score     : {db_score:.2f}")
            else:
                print("⚠ Trop de bruit pour évaluer les clusters correctement.")
        else:
            print("⚠ Clustering invalide (1 seul groupe détecté).")

    def _visualize_clusters(self):
        print("\nVisualisation des clusters (PCA 2D)")
        pca = PCA(n_components=2)
        reduced_data = pca.fit_transform(self.data)
        plt.figure(figsize=(6, 4))
        scatter = plt.scatter(reduced_data[:, 0], reduced_data[:, 1], c=self.labels, cmap='tab10', s=40)
        plt.title("OPTICS - Clustering visualisé avec PCA")
        plt.xlabel("Composante principale 1")
        plt.ylabel("Composante principale 2")
        plt.colorbar(scatter, label="Cluster")
        plt.grid(True)
        plt.show()

    def run_optics_clustering(self, n_iter=10):
        self._optimize_hyperparameters(n_iter=n_iter)
        if self.best_params:
            self._fit_model()
            self._describe_clusters()
            self._evaluate_performance()
            self._visualize_clusters()
        else:
            print("Clustering non exécuté faute de bonne configuration.")
