from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.ensemble import RandomForestRegressor
from sklearn.metrics import mean_squared_error, r2_score
from evaluationModels.evaluation_regressor import RegressionEvaluator
from scipy.stats import uniform as sp_uniform, randint as sp_randint

class Method_RandomForest:
    def __init__(self):
        self.best_parameter = None

    def train_random_forest(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        print("Veuillez patienter quelques instants...")
        rf = RandomForestRegressor()
        param_dist = {
            'n_estimators': sp_randint(10, 200),
            'max_depth': sp_randint(1, 20),
            'min_samples_split': sp_randint(2, 20),
            'min_samples_leaf': sp_randint(1, 20),
            'max_features': sp_uniform(0.1, 0.9)
        }
        random_search = RandomizedSearchCV(rf, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)
        self.best_parameter = random_search.best_estimator_
        print(f"Le modèle Random Forest a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        else:
            print("La prédiction avec les données de test...")
        return self.best_parameter.predict(X_test)

    def run_random_forest_regressor(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle Random Forest______________")
        self.train_random_forest(X_train, y_train)
        y_pred = self.predict(X_test) 
        
        evaluator = RegressionEvaluator(y_test, y_pred) 
        evaluator.evaluation_metrics() 

