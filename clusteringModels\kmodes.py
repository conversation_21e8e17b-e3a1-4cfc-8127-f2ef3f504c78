import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from kmodes.kmodes import KModes

class KModesClustering:
    def __init__(self, data):
        self.data = data
        self.kmode_model = None
        self.clusters = None

    def _find_optimal_k(self):
        print("🔎 Recherche du nombre optimal de clusters (k)...")
        cost = []
        K = list(range(1, 6))  # Tu peux modifier la plage ici

        for num_clusters in K:
            kmode = KModes(n_clusters=num_clusters, init="Cao", n_init=1, verbose=0)
            kmode.fit_predict(self.data)
            cost.append(kmode.cost_)

        # Calcul des variations relatives entre les coûts
        variations = [0]  # Pas de variation pour k=1
        for i in range(1, len(cost)):
            delta = (cost[i - 1] - cost[i]) / cost[i - 1] * 100
            variations.append(round(delta, 2))

        # Détection du k avec la plus forte baisse relative (à partir de k=2)
        optimal_k_index = np.argmax(variations[1:]) + 1
        optimal_k = K[optimal_k_index]

        # Affichage des résultats
        print("\n📊 Résumé des résultats :")
        for k_val, c, v in zip(K, cost, variations):
            print(f"k = {k_val} → Coût = {c:.0f}, Variation = {v:.2f}%")

        # Visualisation du coude
        plt.figure(figsize=(8, 5))
        plt.plot(K, cost, marker='o', linestyle='-')
        for i, c in enumerate(cost):
            plt.text(K[i], c, f'{c:.0f}', ha='center', va='bottom')
        plt.axvline(optimal_k, color='red', linestyle='--', label=f'Optimal k = {optimal_k}')
        plt.xlabel('Nombre de clusters (k)')
        plt.ylabel('Coût')
        plt.title('Méthode du coude avec analyse des variations')
        plt.grid(True)
        plt.legend()
        plt.show()

        print(f"\n✅ Nombre optimal de clusters détecté automatiquement : {optimal_k}")
        return optimal_k

    def _fit_model(self):
        optimal_k = self._find_optimal_k()
        kmode = KModes(n_clusters=optimal_k, init="Cao", n_init=1, verbose=1)
        self.clusters = kmode.fit_predict(self.data)
        self.kmode_model = kmode
        print(f"\n✅ Modèle entraîné avec k = {optimal_k}")

    def _describe_clusters(self):
        if self.clusters is None:
            raise ValueError("Les clusters n'ont pas encore été générés.")
        print("\n📌 Description des clusters :")
        for label in np.unique(self.clusters):
            count = np.sum(self.clusters == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label} : {count} points ({percent:.2f}%)")

    def run_kmodes_clustering(self):
        print("\n▶ ️ Lancement du clustering K-Modes complet...")
        self._fit_model()
        self._describe_clusters()
