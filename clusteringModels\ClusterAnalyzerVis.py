import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from scipy.stats import ttest_ind, chi2_contingency

class ClusterAnalyzer:
    def __init__(self, data, labels):
        self.data = data.reset_index(drop=True).copy()

        # Créer une série avec les labels, alignée sur l’index du DataFrame
        labels_series = pd.Series(labels, index=self.data.index, name='Cluster')

        if 'Cluster' in self.data.columns:
            self.data = self.data.drop(columns='Cluster')

        self.data['Cluster'] = labels_series

        self.n_clusters = len(np.unique(labels))


    def describe_clusters(self):
        print("\n▶ Statistiques descriptives par cluster")
        try:
            print(self.data.groupby('Cluster').describe().T)
        except Exception as e:
            print(f"Erreur dans la description des clusters : {type(e).__name__} - {e}")

    def plot_numerical_distributions(self):
        numerical_cols = [col for col in self.data.select_dtypes(include=['int64', 'float64']).columns if col != 'Cluster']
        if not numerical_cols:
            print("Aucune variable numérique à afficher.")
            return

        print("\n▶ Choisissez le type de graphique pour les variables numériques :")
        print("1. Boxplot")
        print("2. Violinplot")
        print("3. Les deux superposés")

        while True:
            choice = input("Votre choix (1/2/3) : ").strip()
            if choice in ["1", "2", "3"]:
                break
            else:
                print("Entrée invalide. Veuillez choisir 1, 2 ou 3.")

        n_cols = 2
        n_rows = int(np.ceil(len(numerical_cols) / n_cols))

        fig, axes = plt.subplots(n_rows, n_cols, figsize=(14, 5 * n_rows))
        axes = axes.flatten() if len(numerical_cols) > 1 else [axes]

        for i, col in enumerate(numerical_cols):
            ax = axes[i]
            if choice == "1":
                sns.boxplot(x='Cluster', y=col, data=self.data, ax=ax, width=0.4, fliersize=3)
            elif choice == "2":
                sns.violinplot(x='Cluster', y=col, data=self.data, ax=ax, inner="box", alpha=0.6)
            elif choice == "3":
                sns.boxplot(x='Cluster', y=col, data=self.data, ax=ax, width=0.3, fliersize=3)
                sns.violinplot(x='Cluster', y=col, data=self.data, ax=ax, inner=None, alpha=0.3)
            
            ax.set_title(f"{col} par cluster", pad=15)
            ax.set_xlabel("Cluster")
            ax.set_ylabel(col)
            ax.grid(True)

        # Supprimer les axes inutilisés
        for j in range(i + 1, len(axes)):
            fig.delaxes(axes[j])

        fig.suptitle("Distributions numériques par cluster", fontsize=16)
        plt.tight_layout(rect=[0, 0, 1, 0.97])
        plt.show()


    def plot_categorical_distributions(self):
        categorical_cols = self.data.select_dtypes(include=['object', 'category']).columns
        if categorical_cols.empty:
            print("Aucune variable catégorielle à afficher.")
            return

        print("\n▶ Choisissez le type de graphique pour les variables catégorielles :")
        print("1. Barplot")
        print("2. Camembert (pie chart)")
        print("3. Les deux")
        
        while True:
            choice = input("Votre choix (1/2/3) : ").strip()
            if choice in {"1", "2", "3"}:
                break
            else:
                print("Entrée invalide. Veuillez choisir 1, 2 ou 3.")

        for col in categorical_cols:
            counts = self.data.groupby(['Cluster', col]).size().reset_index(name='Count')
            counts['Total'] = counts.groupby('Cluster')['Count'].transform('sum')
            counts['Pourcentage'] = 100 * counts['Count'] / counts['Total']

            # Nombre total de sous-graphiques
            n_charts = 0
            if choice in {"1", "3"}:
                n_charts += 1
            if choice in {"2", "3"}:
                n_charts += self.n_clusters

            fig, axes = plt.subplots(1, n_charts, figsize=(5 * n_charts, 5))

            if n_charts == 1:
                axes = [axes]
            elif isinstance(axes, np.ndarray):
                axes = axes.flatten()
            else:
                axes = [axes]

            plot_idx = 0

            # Barplot
            if choice in {"1", "3"}:
                ax = axes[plot_idx]
                sns.barplot(data=counts, x=col, y='Pourcentage', hue='Cluster', ax=ax)
                for container in ax.containers:
                    ax.bar_label(container, fmt='%.1f%%', label_type='edge')
                ax.set_title(f'{col} par cluster (barplot)')
                ax.set_ylabel("Pourcentage (%)")
                ax.grid(True)
                plot_idx += 1

            # Camemberts
            if choice in {"2", "3"}:
                for cluster_id in sorted(self.data['Cluster'].unique()):
                    ax = axes[plot_idx]
                    subset = counts[counts['Cluster'] == cluster_id]
                    labels = subset[col].astype(str)
                    sizes = subset['Pourcentage']
                    ax.pie(sizes, labels=labels, autopct='%1.1f%%', startangle=140)
                    ax.set_title(f'Cluster {cluster_id} - {col}')
                    plot_idx += 1

            fig.suptitle(f"Distribution de '{col}' par cluster", fontsize=16)
            plt.tight_layout(rect=[0, 0, 1, 0.95])
            plt.show(block=True)  
            
    def feature_importance_analysis(self, significance_level=0.05):
        print("\n▶ Analyse des variables discriminantes par cluster")

        numerical_cols = [col for col in self.data.select_dtypes(include=['int64', 'float64']).columns if col != 'Cluster']
        categorical_cols = self.data.select_dtypes(include=['object', 'category']).columns

        print("\n* Variables numériques : tests ANOVA ou t-test")
        for col in numerical_cols:
            try:
                values = [group[col].values for name, group in self.data.groupby("Cluster")]
                if self.n_clusters == 2:
                    stat, p = ttest_ind(values[0], values[1])
                else:
                    from scipy.stats import f_oneway
                    stat, p = f_oneway(*values)
                if p < significance_level:
                    print(f"{col} : significatif (p={p:.4f})")
            except Exception as e:
                print(f"Erreur avec la variable {col} : {type(e).__name__} - {e}")

        print("\n* Variables catégorielles : test du chi2")
        for col in categorical_cols:
            try:
                crosstab = pd.crosstab(self.data[col], self.data["Cluster"])
                chi2, p, _, _ = chi2_contingency(crosstab)
                if p < significance_level:
                    print(f"{col} : significatif (p={p:.4f})")
            except Exception as e:
                print(f"Erreur avec la variable {col} : {type(e).__name__} - {e}")

    def run_analysis(self):
        print("\n" + "*"*80)
        print("Analyse Post-clustering".center(80))
        print("*"*80)
        self.describe_clusters()
        self.plot_numerical_distributions()
        self.plot_categorical_distributions()
        print('feature importance')
        self.feature_importance_analysis()
        print("\n" + "*"*80)

