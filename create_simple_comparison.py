import matplotlib.pyplot as plt
import numpy as np
import pandas as pd

# Set style
plt.style.use('default')

# Data for all models
models = ['Random Forest', 'Decision Tree', 'Linear Regression', 'Extra Trees', 'XGBoost', 'Gradient Boosting', 'KNN']
r2_scores = [0.7911, 0.7908, 0.7899, 0.7893, 0.7889, 0.7858, 0.7831]
mse_scores = [0.2123, 0.2126, 0.2136, 0.2142, 0.2146, 0.2177, 0.2205]
rmse_scores = [0.4608, 0.4611, 0.4621, 0.4628, 0.4632, 0.4666, 0.4696]
mae_scores = [0.3998, 0.4002, 0.4013, 0.4016, 0.4015, 0.4046, 0.4059]

# Sort by R² score (descending)
sorted_indices = np.argsort(r2_scores)[::-1]
models_sorted = [models[i] for i in sorted_indices]
r2_sorted = [r2_scores[i] for i in sorted_indices]

print("Creating comparison charts...")

# 1. Main R² comparison chart
plt.figure(figsize=(12, 6))
colors = ['#2E8B57', '#4682B4', '#DAA520', '#CD853F', '#9370DB', '#DC143C', '#FF6347']
bars = plt.bar(models_sorted, r2_sorted, color=colors)

# Add value labels on bars
for i, (bar, score) in enumerate(zip(bars, r2_sorted)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.001, 
             f'{score:.4f}', ha='center', va='bottom', fontweight='bold', fontsize=10)

plt.title('Comparaison des Performances des Modèles de Régression (R²)', fontsize=14, fontweight='bold')
plt.ylabel('Coefficient de Détermination (R²)', fontsize=12)
plt.xlabel('Modèles de Régression', fontsize=12)
plt.xticks(rotation=45, ha='right')
plt.ylim(0.78, 0.795)
plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('images/comparaison_modeles_regression_complete.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 48 created: comparaison_modeles_regression_complete.png")

# 2. 4-panel analysis
fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(14, 10))

# R² comparison
mse_sorted = [mse_scores[i] for i in sorted_indices]
rmse_sorted = [rmse_scores[i] for i in sorted_indices]

ax1.bar(models_sorted, r2_sorted, color='skyblue', alpha=0.8)
ax1.set_title('R² Score', fontsize=12, fontweight='bold')
ax1.set_ylabel('R²', fontsize=10)
ax1.tick_params(axis='x', rotation=45, labelsize=8)
for i, v in enumerate(r2_sorted):
    ax1.text(i, v + 0.0005, f'{v:.4f}', ha='center', va='bottom', fontsize=8)

# MSE comparison
ax2.bar(models_sorted, mse_sorted, color='lightcoral', alpha=0.8)
ax2.set_title('MSE Score', fontsize=12, fontweight='bold')
ax2.set_ylabel('MSE', fontsize=10)
ax2.tick_params(axis='x', rotation=45, labelsize=8)
for i, v in enumerate(mse_sorted):
    ax2.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=8)

# RMSE comparison
ax3.bar(models_sorted, rmse_sorted, color='lightgreen', alpha=0.8)
ax3.set_title('RMSE Score', fontsize=12, fontweight='bold')
ax3.set_ylabel('RMSE', fontsize=10)
ax3.tick_params(axis='x', rotation=45, labelsize=8)
for i, v in enumerate(rmse_sorted):
    ax3.text(i, v + 0.002, f'{v:.4f}', ha='center', va='bottom', fontsize=8)

# Performance ranking
ax4.barh(range(len(models_sorted)), r2_sorted, color='gold', alpha=0.8)
ax4.set_yticks(range(len(models_sorted)))
ax4.set_yticklabels(models_sorted, fontsize=8)
ax4.set_title('Classement par R²', fontsize=12, fontweight='bold')
ax4.set_xlabel('R²', fontsize=10)
for i, v in enumerate(r2_sorted):
    ax4.text(v + 0.0005, i, f'{v:.4f}', ha='left', va='center', fontsize=8)

plt.tight_layout()
plt.savefig('images/analyse_complete_modeles_regression.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 49 created: analyse_complete_modeles_regression.png")

# 3. Detailed metrics comparison
fig, axes = plt.subplots(2, 2, figsize=(14, 10))
metrics = ['R²', 'MSE', 'RMSE', 'MAE']
mae_sorted = [mae_scores[i] for i in sorted_indices]
data = [r2_sorted, mse_sorted, rmse_sorted, mae_sorted]
colors_metrics = ['steelblue', 'crimson', 'forestgreen', 'darkorange']

for i, (ax, metric, values, color) in enumerate(zip(axes.flat, metrics, data, colors_metrics)):
    bars = ax.bar(models_sorted, values, color=color, alpha=0.7)
    ax.set_title(f'{metric} - Comparaison', fontsize=12, fontweight='bold')
    ax.set_ylabel(metric, fontsize=10)
    ax.tick_params(axis='x', rotation=45, labelsize=8)
    
    for bar, value in zip(bars, values):
        height = bar.get_height()
        ax.text(bar.get_x() + bar.get_width()/2., height + height*0.01,
                f'{value:.4f}', ha='center', va='bottom', fontsize=8)

plt.tight_layout()
plt.savefig('images/metriques_detaillees_modeles.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 50 created: metriques_detaillees_modeles.png")

# 4. Performance gap analysis
plt.figure(figsize=(12, 6))
best_r2 = max(r2_sorted)
gaps = [best_r2 - score for score in r2_sorted]

bars = plt.bar(models_sorted, gaps, color=['green' if gap == 0 else 'orange' if gap < 0.005 else 'red' for gap in gaps])
plt.xlabel('Modèles de Régression', fontsize=12)
plt.ylabel('Écart par rapport au meilleur modèle', fontsize=12)
plt.title('Analyse de l\'Écart de Performance (R²)', fontsize=14, fontweight='bold')
plt.xticks(rotation=45, ha='right')

for i, (bar, gap) in enumerate(zip(bars, gaps)):
    plt.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.0002,
             f'{gap:.4f}', ha='center', va='bottom', fontsize=10)

plt.grid(axis='y', alpha=0.3)
plt.tight_layout()
plt.savefig('images/analyse_ecart_performance.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 52 created: analyse_ecart_performance.png")

# 5. Summary table as image
fig, ax = plt.subplots(figsize=(12, 8))
ax.axis('tight')
ax.axis('off')

# Create summary table
summary_data = {
    'Rang': list(range(1, len(models_sorted) + 1)),
    'Modèle': models_sorted,
    'R²': [f'{score:.4f}' for score in r2_sorted],
    'MSE': [f'{score:.4f}' for score in mse_sorted],
    'RMSE': [f'{score:.4f}' for score in rmse_sorted],
    'MAE': [f'{score:.4f}' for score in mae_sorted],
    'Écart': [f'{gap:.4f}' for gap in gaps]
}

df = pd.DataFrame(summary_data)
table = ax.table(cellText=df.values, colLabels=df.columns, cellLoc='center', loc='center')
table.auto_set_font_size(False)
table.set_fontsize(10)
table.scale(1.2, 2)

# Color code the ranking
for i in range(len(models_sorted)):
    if i == 0:  # Best model
        table[(i+1, 0)].set_facecolor('#90EE90')  # Light green
        table[(i+1, 1)].set_facecolor('#90EE90')
    elif i <= 2:  # Top 3
        table[(i+1, 0)].set_facecolor('#FFE4B5')  # Light orange
        table[(i+1, 1)].set_facecolor('#FFE4B5')

plt.title('Tableau Récapitulatif des Performances - Tous Modèles', 
          fontsize=14, fontweight='bold', pad=20)
plt.savefig('images/tableau_recapitulatif_performances.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 54 created: tableau_recapitulatif_performances.png")

print("\n🎯 All comparison charts created successfully!")
print(f"\n🏆 Final ranking by R²:")
for i, (model, score) in enumerate(zip(models_sorted, r2_sorted), 1):
    print(f"{i}. {model}: {score:.4f}")
