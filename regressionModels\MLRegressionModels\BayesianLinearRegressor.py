import numpy as np
import pymc as pm
import arviz as az
import random
import itertools
from sklearn.metrics import r2_score
from evaluationModels.evaluation_regressor import RegressionEvaluator

class Method_BayesianLinear_Regressor:
    def __init__(self):
        self.best_params = None
        self.best_r2 = -np.inf
        self.model = None
        self.trace = None
        self.y_pred = None

    def train_bayesian(self, X_train, y_train, mu_values=[0, 1, -1], sigma_values=[1, 5, 10], n_iter=5):
            param_combinations = list(itertools.product(mu_values, sigma_values))
            sampled_params = random.sample(param_combinations, min(n_iter, len(param_combinations)))

            for mu, sigma in sampled_params:
                with pm.Model() as model:
                    coeffs = pm.Normal('coeffs', mu=mu, sigma=sigma, shape=X_train.shape[1])
                    intercept = pm.Normal('intercept', mu=mu, sigma=sigma)
                    sigma_noise = pm.HalfNormal('sigma', sigma=5)

                    mu_pred = pm.math.dot(X_train, coeffs) + intercept
                    Y_obs = pm.Normal('Y_obs', mu=mu_pred, sigma=sigma_noise, observed=y_train)

                    trace = pm.sample(500, tune=500, target_accept=0.9, progressbar=False, chains=1)

                a = trace.posterior['coeffs'].mean(dim=["chain", "draw"]).values
                b = trace.posterior['intercept'].mean(dim=["chain", "draw"]).values
                y_pred_train = np.dot(X_train, a) + b
                r2 = r2_score(y_train, y_pred_train)

                if r2 > self.best_r2:
                    self.best_r2 = r2
                    self.best_params = {'mu': mu, 'sigma': sigma}
                    self.trace = trace
                    self.model = model

            print(f"Meilleurs hyperparamètres : {self.best_params}, R² (train) = {self.best_r2:.4f}")



    def predict(self, X_test):
        if self.trace is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        a = self.trace.posterior['coeffs'].mean(dim=["chain", "draw"]).values
        b = self.trace.posterior['intercept'].mean(dim=["chain", "draw"]).values
        y_pred = np.dot(X_test, a) + b
        self.y_pred = y_pred
        return y_pred

    def run_bayesian_linear_regressor(self, X_train, y_train, X_test, y_test):
        print("Entraînement du modèle Bayesian Linear Regression (PyMC)")
        self.train_bayesian(X_train, y_train)
        y_pred = self.predict(X_test)
        print("Évaluation du modèle")
        evaluator = RegressionEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
