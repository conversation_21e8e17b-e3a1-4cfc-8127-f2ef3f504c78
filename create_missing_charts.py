import matplotlib.pyplot as plt
import numpy as np
from math import pi

# Data for all models
models = ['Random Forest', 'Decision Tree', 'Linear Regression', 'Extra Trees', 'XGBoost', 'Gradient Boosting', 'KNN']
r2_scores = [0.7911, 0.7908, 0.7899, 0.7893, 0.7889, 0.7858, 0.7831]
mse_scores = [0.2123, 0.2126, 0.2136, 0.2142, 0.2146, 0.2177, 0.2205]
rmse_scores = [0.4608, 0.4611, 0.4621, 0.4628, 0.4632, 0.4666, 0.4696]
mae_scores = [0.3998, 0.4002, 0.4013, 0.4016, 0.4015, 0.4046, 0.4059]

# Sort by R² score (descending)
sorted_indices = np.argsort(r2_scores)[::-1]
models_sorted = [models[i] for i in sorted_indices]
r2_sorted = [r2_scores[i] for i in sorted_indices]
mse_sorted = [mse_scores[i] for i in sorted_indices]
rmse_sorted = [rmse_scores[i] for i in sorted_indices]
mae_sorted = [mae_scores[i] for i in sorted_indices]

print("Creating missing charts...")

# Figure 51: Radar chart for top 5 models
top_5_models = models_sorted[:5]
top_5_r2 = r2_sorted[:5]

# Normalize metrics for radar chart (higher is better for all)
top_5_mse_norm = [1 - (x - min(mse_sorted)) / (max(mse_sorted) - min(mse_sorted)) for x in mse_sorted[:5]]
top_5_rmse_norm = [1 - (x - min(rmse_sorted)) / (max(rmse_sorted) - min(rmse_sorted)) for x in rmse_sorted[:5]]
top_5_mae_norm = [1 - (x - min(mae_sorted)) / (max(mae_sorted) - min(mae_sorted)) for x in mae_sorted[:5]]
top_5_r2_norm = [(x - min(r2_sorted)) / (max(r2_sorted) - min(r2_sorted)) for x in r2_sorted[:5]]

# Categories for radar chart
categories = ['R²', 'MSE (inv)', 'RMSE (inv)', 'MAE (inv)']
N = len(categories)

# Compute angle for each axis
angles = [n / float(N) * 2 * pi for n in range(N)]
angles += angles[:1]

# Create radar chart
fig, ax = plt.subplots(figsize=(10, 8), subplot_kw=dict(projection='polar'))

# Colors for top 5 models
radar_colors = ['#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd']

for i, model in enumerate(top_5_models):
    values = [top_5_r2_norm[i], top_5_mse_norm[i], top_5_rmse_norm[i], top_5_mae_norm[i]]
    values += values[:1]
    
    ax.plot(angles, values, 'o-', linewidth=2, label=model, color=radar_colors[i])
    ax.fill(angles, values, alpha=0.25, color=radar_colors[i])

# Add category labels
ax.set_xticks(angles[:-1])
ax.set_xticklabels(categories, fontsize=11)
ax.set_ylim(0, 1)
ax.set_title('Comparaison Radar des Top 5 Modèles\n(Toutes Métriques Normalisées)', 
             size=14, fontweight='bold', pad=20)
ax.legend(loc='upper right', bbox_to_anchor=(1.2, 1.0))
ax.grid(True)

plt.tight_layout()
plt.savefig('images/radar_chart_top5_modeles.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 51 created: radar_chart_top5_modeles.png")

# Figure 53: Performance vs Complexity analysis
complexity_scores = [8, 3, 1, 7, 9, 6, 2]  # Subjective complexity ranking (1=simple, 10=complex)
complexity_sorted = [complexity_scores[i] for i in sorted_indices]

plt.figure(figsize=(10, 7))
scatter = plt.scatter(complexity_sorted, r2_sorted, s=200, c=range(len(models_sorted)), 
                     cmap='viridis', alpha=0.7, edgecolors='black', linewidth=2)

# Add model labels
for i, model in enumerate(models_sorted):
    plt.annotate(model, (complexity_sorted[i], r2_sorted[i]), 
                xytext=(5, 5), textcoords='offset points', fontsize=9, fontweight='bold')

plt.xlabel('Complexité du Modèle (1=Simple, 10=Complexe)', fontsize=12)
plt.ylabel('Performance (R²)', fontsize=12)
plt.title('Analyse Efficacité: Performance vs Complexité', fontsize=14, fontweight='bold')
plt.grid(True, alpha=0.3)
plt.colorbar(scatter, label='Rang de Performance')
plt.tight_layout()
plt.savefig('images/performance_vs_complexite.png', dpi=300, bbox_inches='tight')
plt.close()
print("✓ Figure 53 created: performance_vs_complexite.png")

print("\n🎯 Missing charts created successfully!")
print("All 7 comparison figures are now available in the images folder.")
