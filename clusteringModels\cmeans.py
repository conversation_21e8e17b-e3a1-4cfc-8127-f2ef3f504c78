import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from sklearn.metrics.pairwise import euclidean_distances
from sklearn.metrics import silhouette_score
from mpl_toolkits.mplot3d import Axes3D
import skfuzzy as fuzz

class CMeansClustering:
    def __init__(self, data, max_clusters=10, max_iter=100, m=2):
        self.data = data
        self.max_clusters = max_clusters
        self.max_iter = max_iter
        self.m = m
        self.centroids = None
        self.labels = None

    def run_c_means_clustering(self):
        print("______________Formation du modèle Cmeans______________")
        data = self.data.T  # Transpose data for skfuzzy
        distortions = []
        silhouette_scores = []
        max_silhouette_score = -1
        max_silhouette_k = -1
        optimal_clusters_data = []

        for i in range(2, self.max_clusters + 1):  # C-Means requires at least 2 clusters
            cntr, u, _, _, _, _, fpc = fuzz.cluster.cmeans(data, i, self.m, error=0.005, maxiter=self.max_iter, init=None)
            cluster_labels = np.argmax(u, axis=0)
            distortions.append(fpc)  # Using fuzzy partition coefficient as a measure
            
            silhouette_score_val = silhouette_score(data.T, cluster_labels)
            silhouette_scores.append(silhouette_score_val)
            if silhouette_score_val > max_silhouette_score:
                max_silhouette_score = silhouette_score_val
                max_silhouette_k = i

        cntr, u, _, _, _, _, fpc = fuzz.cluster.cmeans(data, max_silhouette_k, self.m, error=0.005, maxiter=self.max_iter, init=None)
        self.centroids = cntr
        self.labels = np.argmax(u, axis=0)

        print("Le nombre de clusters optimal est : ", max_silhouette_k)
        print("Le coefficient de silhouette : ", max_silhouette_score)
        print("------------------------------------------------------------------- \n")
        print("Description de chaque cluster \n ")
        
        # Stocker les données des clusters pour la valeur de K optimale
        cluster_percentages = []
        for cluster_label in range(max_silhouette_k):
            cluster_data = self.data[self.labels == cluster_label]
            optimal_clusters_data.append(pd.DataFrame(cluster_data, columns=range(self.data.shape[1])))

            # Calculer le pourcentage d'éléments dans ce cluster
            percentage = len(cluster_data) / len(self.data) * 100
            cluster_percentages.append(percentage)

            print(f"Cluster {cluster_label+1}: contient {len(cluster_data)} éléments et sa proportion du dataset = {percentage:.2f}%\n")
            print(cluster_data)
            print("\n")

        # Tracer la courbe de Elbow
        plt.figure(figsize=(8, 4))
        plt.plot(range(2, self.max_clusters + 1), distortions, marker='o')
        plt.title('Courbe de Elbow')
        plt.xlabel('Nombre de K clusters')
        plt.ylabel('Distortion')
        plt.xticks(np.arange(2, self.max_clusters + 1))
        plt.grid(True)
        plt.show()

        # Tracer le score de silhouette
        if silhouette_scores:
            plt.figure(figsize=(8, 4))
            plt.plot(range(2, self.max_clusters + 1), silhouette_scores, marker='o')
            plt.title('Les coefficients de silhouette')
            plt.xlabel('Nombre de K clusters')
            plt.ylabel('Score de silhouette')
            plt.xticks(np.arange(2, self.max_clusters + 1))
            plt.grid(True)
            plt.show()
        
        # Affichage des graphes 2D ou 3D
        a = self.data.shape[1]
        if a == 2 : 
            self.visualisation_2(self.centroids, self.labels) 
        if a == 3 : 
            self.visualisation_3(self.centroids, self.labels)

    def visualisation_2(self, centroids, labels):
        data = self.data.to_numpy()
        X1 = data[:, 0]
        X2 = data[:, 1]
        
        distances = euclidean_distances(data, centroids)

        radius = np.zeros(len(centroids))
        for i in range(len(centroids)):
            radius[i] = np.max(distances[labels == i, i])
        
        plt.scatter(X1, X2, c=labels, s=50, label='individu de chaque cluster')
        plt.scatter(centroids[:, 0], centroids[:, 1], marker='o', c='red', s=20, label='centroides des clusters')

        for i, txt in enumerate(range(len(data))):
            plt.text(X1[i], X2[i], str(txt), fontsize=10, ha='right')

        for i, centroid in enumerate(centroids):
            circle = plt.Circle(centroid, radius[i], color='black', fill=False)
            plt.gca().add_patch(circle)

        plt.title('Clustering avec C-Means')
        plt.xlabel('X1')
        plt.ylabel('X2')
        plt.legend()
        plt.show()

    def visualisation_3(self, centroids, labels): 
        fig = plt.figure(figsize=(8, 6))
        ax = fig.add_subplot(111, projection='3d')

        unique_labels = np.unique(labels)
        colors = plt.cm.rainbow(np.linspace(0, 1, len(unique_labels)))

        for i, label in enumerate(unique_labels):
            cluster_data = self.data[self.data.columns].values[labels == label]
            ax.scatter(cluster_data[:, 0], cluster_data[:, 1], cluster_data[:, 2], s=20, color=colors[i], label=f'Cluster {label}')

        ax.scatter(centroids[:, 0], centroids[:, 1], centroids[:, 2], s=100, c='black', label='Centroides')

        ax.set_xlabel('X1')
        ax.set_ylabel('X2')
        ax.set_zlabel('X3')
        ax.set_title('Clustering avec C-Means')
        ax.legend()
        plt.show()
