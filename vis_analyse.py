import pandas as pd
import numpy as np
import seaborn as sns
import matplotlib.pyplot as plt
from scipy.stats import ttest_ind, chi2_contingency
from IPython.display import display
from preprocessor import DataPreprocessor
import math

class DataAnalyzer:
    def __init__(self, data):
        self.data = data

    def info_data(self):
        print("\n" + "="*80)
        print("Visualisation initiale des données".center(80))
        print("="*80) 

        print("\n▶ Cinq premières et derniéres lignes du jeu de données:")
        print(self.data)

        print("\n▶ Nombre total de lignes et de colonnes:")
        print(self.data.shape)

        print("\n▶ Noms des colonnes:")
        print(self.data.columns.tolist())

        print("\n▶ Types de données par colonne:")
        print(self.data.dtypes)


    def summarize_statistics(self):
        print("\n" + "="*80)
        print("Statistiques descriptives".center(80))
        print("="*80)

        # Statistiques pour les variables quantitatives
        if len(self.data.select_dtypes(include=['float64', 'int64']).columns) > 0:
            print("\n▶ Statistique descriptive pour les variables quantitatives")
            summary = self.data.describe()
            print(summary)
        else:
            print("Il n'y a pas de variables quantitatives dans le dataset.")

        # Statistiques pour les variables qualitatives
        if len(self.data.select_dtypes(include=['object']).columns) > 0:
            print("\n▶ Statistique descriptive pour les variables qualitatives")
            qualitative_summary = self.data.describe(include=['object'])
            print(qualitative_summary)
        else:
            print("Il n'y a pas de variables qualitatives dans le dataset.")

    def visualize_data(self): #this function is modified bu tetsing team
        continuous_columns = self.data.select_dtypes(include=['int64', 'int32', 'float64', 'float32']).columns
        categorical_columns = self.data.select_dtypes(include=['object', 'category']).columns
        continuous_data = self.data.select_dtypes(include=['float64', 'int64'])
        print("\n" + "="*80)
        print("Visualisations graphiques".center(80))
        print("="*80)
        if not continuous_columns.empty:
            while True:
                visualize = input(
                    "\n▶ Voulez-vous visualiser les boxplots des variables continues?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()

                if visualize == "1":
                    num_cols = 3
                    num_rows = math.ceil(len(continuous_data.columns) / num_cols)

                    fig, axes = plt.subplots(nrows=num_rows, ncols=num_cols, figsize=(15, 4*num_rows))
                    axes = axes.flatten()

                    for i, column in enumerate(continuous_data.columns):
                        sns.boxplot(x=continuous_data[column], ax=axes[i])
                        axes[i].set_title(f'Boxplot of {column}')
                        axes[i].set_xlabel('Value')

                    # Turn off unused subplots
                    for j in range(i+1, len(axes)):
                        axes[j].axis('off')

                    plt.tight_layout()
                    plt.show()
                    break  # Exit loop after visualization

                elif visualize == "2":
                    print("Visualisation des boxplots ignorée.")
                    break  # Exit loop without visualization

                else:
                    print("Entrée invalide. Veuillez entrer '1' pour Oui ou '2' pour Non.")

        # Visualize Continuous Variables
        if len(continuous_columns) > 0:
            while True:
                visualize_continuous = input(
                    "\n▶ Voulez-vous visualiser les variables continues avec un pairplot?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()

                if visualize_continuous == "1":
                    sns.pairplot(data=self.data[continuous_columns])
                    plt.suptitle('Pairplot of Continuous Variables', y=1.02)  # Adjust title position
                    plt.show()
                    break
                elif visualize_continuous == "2":
                    print("Visualisation des variables continues ignorée.")
                    break
                else:
                    print("Entrée invalide. Veuillez entrer '1' pour Oui ou '2' pour Non.")

        # Visualize Categorical Variables
        if len(categorical_columns) > 0:
            while True:
                visualize_categorical = input(
                    "\n▶ Voulez-vous visualiser les variables catégoriques avec des diagrammes en camembert?\n"
                    "1. Oui\n"
                    "2. Non\n"
                    "Veuillez entrer votre choix (1-2) : "
                ).strip()

                if visualize_categorical == "1":
                    for column in categorical_columns:
                        category_counts = self.data[column].value_counts()
                        plt.figure(figsize=(8, 6))
                        plt.pie(category_counts, labels=category_counts.index, autopct='%1.1f%%', startangle=140)
                        plt.axis('equal')
                        plt.title(column)
                        plt.show()
                    break
                elif visualize_categorical == "2":
                    print("Visualisation des variables catégoriques ignorée.")
                    break
                else:
                    print("Entrée invalide. Veuillez entrer '1' pour Oui ou '2' pour Non.")
      
    def relation_categorical(self, target, significance_level=0.05):
        categorical_vars = [col for col in self.data.columns if self.data[col].dtype == 'object']

        if not categorical_vars:
            print("Aucune variable catégorielle trouvée dans le jeu de données.")
            return

        crosstabs = {}
        chi2_stats = {}
        for var in categorical_vars:
            crosstab = pd.crosstab(self.data[var], self.data[target])
            crosstabs[var] = crosstab

            chi2, p, _, _ = chi2_contingency(crosstab)
            chi2_stats[var] = (chi2, p)

        for var, crosstab in crosstabs.items():
            plt.figure(figsize=(8, 6))
            sns.heatmap(crosstab, annot=True, cmap='coolwarm', fmt=".2f")
            plt.title(f"Relation entre {target} et {var}")
            plt.xlabel(target)
            plt.ylabel(var)
            plt.show()

        ranked_features = sorted(chi2_stats.items(), key=lambda x: x[1][0], reverse=True)
        significant_features = [(var, (chi2, p)) for var, (chi2, p) in ranked_features if p < significance_level]
        print("\nCaractéristiques significatives:")
        for i, (var, (chi2, p)) in enumerate(significant_features):
            print(f"{i + 1}. Variable '{var}' - Chi-deux statistique: {chi2}, P-value: {p}")

        return ranked_features

    def relation_continuous(self, target, significance_level=0.05):
        continuous_vars = [col for col in self.data.columns if self.data[col].dtype != 'object']

        if not continuous_vars:
            print("Aucune variable continue trouvée dans le jeu de données.")
            return

        plt.figure(figsize=(8, 8))
        heatmap = sns.heatmap(self.data[continuous_vars].corr(), annot=True, cmap='Blues', cbar=False)

        t_test_results = {}
        for var in continuous_vars:
            t_stat, p_value = ttest_ind(self.data[self.data[target] == 0][var], self.data[self.data[target] == 1][var])
            t_test_results[var] = (t_stat, p_value)

        ranked_features = sorted(t_test_results.items(), key=lambda x: abs(x[1][0]), reverse=True)

        significant_features = [(var, (t_stat, p)) for var, (t_stat, p) in ranked_features if p < significance_level]
        print("\nCaractéristiques significatives :")
        for i, (var, (t_stat, p)) in enumerate(significant_features):
            print(f"{i + 1}. Variable '{var}' - t-test statistic: {t_stat}, P-value: {p}")

        plt.title("Carte de corrélation des variables continues")
        plt.show()

        return ranked_features

    def visualization(self):
        
        self.summarize_statistics()
        self.visualize_data()