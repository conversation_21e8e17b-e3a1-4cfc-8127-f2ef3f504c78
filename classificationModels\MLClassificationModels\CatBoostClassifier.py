

from catboost import CatBoostClassifier
from sklearn.model_selection import RandomizedSearchCV
from sklearn.metrics import make_scorer, accuracy_score
from scipy.stats import randint, uniform
import numpy as np
from evaluationModels.evaluation_classification import ClassifierEvaluator

class Method_CatBoost_Classifier:
    def __init__(self):
        self.best_parameter = None

    def train_catboost(self, X_train, y_train, n_iter=20, cv=5, random_state=42):
        print("Optimisation des hyperparamètres avec RandomizedSearchCV...")

        # Détection du type de classification (binaire ou multiclasse)
        n_classes = len(np.unique(y_train))
        if n_classes == 2:
            loss_function = 'Logloss'
        else:
            loss_function = 'MultiClass'

        # Espace de recherche pour les hyperparamètres
        param_dist = {
            'iterations': randint(100, 1000),
            'depth': randint(4, 10),
            'learning_rate': uniform(0.01, 0.3),
            'l2_leaf_reg': uniform(1, 10),
            'border_count': randint(32, 255),
            'bagging_temperature': uniform(0, 1),
            'random_strength': uniform(0, 1),
            'grow_policy': ['SymmetricTree', 'Depthwise', 'Lossguide']
        }

        # Modèle CatBoost avec apprentissage silencieux
        model = CatBoostClassifier(
            loss_function=loss_function,
            eval_metric='Accuracy',
            random_state=random_state,
            verbose=0
        )

        # Recherche aléatoire des meilleurs hyperparamètres
        random_search = RandomizedSearchCV(
            estimator=model,
            param_distributions=param_dist,
            n_iter=n_iter,
            scoring=make_scorer(accuracy_score),
            cv=cv,
            random_state=random_state,
            n_jobs=-1
        )

        random_search.fit(X_train, y_train)
        self.best_parameter = random_search.best_estimator_

        print(f"Meilleurs hyperparamètres : {random_search.best_params_}")
        print(f"Score de validation croisée : {random_search.best_score_:.4f}")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        print("Prédiction avec le modèle optimal...")
        return self.best_parameter.predict(X_test)

    def run_catboost_classifier(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle CatBoost______________")
        self.train_catboost(X_train, y_train)
        y_pred = self.predict(X_test)
        print('_________________Évaluation du modèle_________________')
        evaluator = ClassifierEvaluator(y_test, y_pred)
        evaluator.evaluation_metrics()
