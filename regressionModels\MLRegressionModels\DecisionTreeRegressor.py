from sklearn.model_selection import train_test_split, RandomizedSearchCV
from sklearn.tree import DecisionTreeRegressor
from sklearn.metrics import mean_squared_error, r2_score
from evaluationModels.evaluation_regressor import RegressionEvaluator
from scipy.stats import uniform as sp_uniform, randint as sp_randint

class Method_DecisionTree:
    def __init__(self):
        self.best_parameter = None


    def train_decision_tree(self, X_train, y_train, n_iter=100, cv=5, random_state=42):
        print("Veuillez patienter quelques instants...")
        dt = DecisionTreeRegressor()
        param_dist = {
            'max_depth': sp_randint(1, 20),
            'min_samples_split': sp_randint(2, 20),
            'min_samples_leaf': sp_randint(1, 20),
            'max_features': sp_uniform(0.1, 0.9)
        }
        random_search = RandomizedSearchCV(dt, param_distributions=param_dist, n_iter=n_iter, cv=cv, random_state=random_state, n_jobs=-1)
        random_search.fit(X_train, y_train)
        self.best_parameter = random_search.best_estimator_
        print(f"Le modèle Decision Tree a été entraîné avec les meilleurs hyperparamètres: {random_search.best_params_}.")

        return self

    def predict(self, X_test):
        if self.best_parameter is None:
            raise ValueError("Le modèle n'a pas été entraîné.")
        else:
            print("La prédiction avec les données de test...")
        return self.best_parameter.predict(X_test)


    def run_decision_tree_regressor(self, X_train, y_train, X_test, y_test):
        print("______________Entraînement du modèle Decision Tree______________")
        self.train_decision_tree(X_train, y_train)
        y_pred = self.predict(X_test) 
        # Évaluation du modèle

        
        evaluator = RegressionEvaluator(y_test, y_pred) # Assurez-vous que cette classe est définie ailleurs
        evaluator.evaluation_metrics()

