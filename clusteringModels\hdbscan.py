import pandas as pd
import numpy as np

import hdbscan
from sklearn.metrics import silhouette_score
from sklearn.metrics.pairwise import euclidean_distances

import matplotlib.pyplot as plt
import seaborn as sns
from mpl_toolkits.mplot3d import Axes3D

from sklearn.manifold import TSNE
from sklearn.decomposition import PCA

class HDBSCANClustering:
    def __init__(self, data):
        self.data = data
        self.model = None
        self.labels = None
        self.best_params = None
        self.silhouette_avg = None

    def _optimize_parameters(self):
        print("Optimisation des hyperparamètres HDBSCAN...")

        # Paramètres à tester
        min_cluster_sizes = [5, 10, 15, 20, 25, 30]
        min_samples = [5, 10, 15, 20]

        best_score = -1
        best_params = {}
        best_labels = None

        results = []

        for min_cluster_size in min_cluster_sizes:
            for min_sample in min_samples:
                # Ignorer les combinaisons invalides
                if min_sample > min_cluster_size:
                    continue

                # Entraîner le modèle
                model = hdbscan.HDBSCAN(
                    min_cluster_size=min_cluster_size,
                    min_samples=min_sample,
                    metric='euclidean',
                    cluster_selection_method='eom'
                )

                model.fit(self.data)
                labels = model.labels_

                # Calculer le score de silhouette si au moins 2 clusters (hors bruit)
                n_clusters = len(set(labels)) - (1 if -1 in labels else 0)

                # Stocker les résultats
                result = {
                    'min_cluster_size': min_cluster_size,
                    'min_samples': min_sample,
                    'n_clusters': n_clusters,
                    'noise_points': list(labels).count(-1),
                    'silhouette': None
                }

                # Calculer le score de silhouette uniquement si au moins 2 clusters
                if n_clusters >= 2:
                    # Filtrer les points de bruit pour le calcul du score de silhouette
                    mask = labels != -1
                    if sum(mask) > 0:  # S'assurer qu'il reste des points après filtrage
                        silhouette_avg = silhouette_score(self.data[mask], labels[mask])
                        result['silhouette'] = silhouette_avg

                        if silhouette_avg > best_score:
                            best_score = silhouette_avg
                            best_params = {
                                'min_cluster_size': min_cluster_size,
                                'min_samples': min_sample
                            }
                            best_labels = labels

                results.append(result)

        # Afficher les résultats sous forme de tableau
        results_df = pd.DataFrame(results)
        print("\nRésultats de l'optimisation des paramètres:")
        print(results_df.sort_values(by='silhouette', ascending=False).head(10))

        if best_score == -1:
            print("\nAucune combinaison de paramètres n'a produit au moins 2 clusters. Utilisation des paramètres par défaut.")
            self.best_params = {'min_cluster_size': 5, 'min_samples': 5}
        else:
            print(f"\nMeilleurs paramètres: min_cluster_size={best_params['min_cluster_size']}, min_samples={best_params['min_samples']}")
            print(f"Score de silhouette: {best_score:.4f}")
            self.best_params = best_params
            self.silhouette_avg = best_score

    def _fit_model(self):
        print("\nEntraînement du modèle HDBSCAN avec les meilleurs paramètres...")
        self.model = hdbscan.HDBSCAN(
            min_cluster_size=self.best_params['min_cluster_size'],
            min_samples=self.best_params['min_samples'],
            metric='euclidean',
            cluster_selection_method='eom'
        )
        self.model.fit(self.data)
        self.labels = self.model.labels_

    def _describe_clusters(self):
        unique_labels = set(self.labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        n_noise = list(self.labels).count(-1)

        print(f"\nNombre de clusters: {n_clusters}")
        print(f"Nombre de points de bruit: {n_noise} ({n_noise/len(self.labels)*100:.2f}%)")

        print("\nDescription des clusters:")
        for label in sorted(unique_labels):
            if label == -1:
                continue  # Traiter les points de bruit séparément
            count = np.sum(self.labels == label)
            percent = 100 * count / len(self.data)
            print(f"Cluster {label}: {count} points ({percent:.2f}%)")

    def _visualize_clusters(self):
        # Vérifier la dimensionnalité des données
        dims = self.data.shape[1]

        # Si les données sont déjà en 2D, les utiliser directement
        if dims == 2:
            X_2d = self.data
            title_suffix = "(données originales)"
        # Sinon, réduire à 2D avec t-SNE pour la visualisation
        else:
            print("\nRéduction de dimensionnalité avec t-SNE pour la visualisation...")
            X_2d = TSNE(n_components=2, random_state=42).fit_transform(self.data)
            title_suffix = "(projection t-SNE)"

        # Créer un joli graphique avec seaborn
        plt.figure(figsize=(10, 8))

        # Palette de couleurs (avec une couleur spéciale pour le bruit)
        unique_labels = set(self.labels)
        n_clusters = len(unique_labels) - (1 if -1 in unique_labels else 0)
        palette = sns.color_palette("husl", n_clusters)

        # Tracer les points de bruit en gris
        noise_mask = self.labels == -1
        if np.any(noise_mask):
            plt.scatter(
                X_2d[noise_mask, 0],
                X_2d[noise_mask, 1],
                c="lightgrey",
                marker=".",
                s=50,
                alpha=0.5,
                label="Bruit"
            )

        # Tracer chaque cluster avec une couleur différente
        color_idx = 0
        for label in sorted(unique_labels):
            if label == -1:
                continue  # Déjà traité

            mask = self.labels == label
            plt.scatter(
                X_2d[mask, 0],
                X_2d[mask, 1],
                c=[palette[color_idx]],
                marker="o",
                s=80,
                alpha=0.8,
                label=f"Cluster {label}"
            )
            color_idx += 1

        plt.title(f"Clustering HDBSCAN {title_suffix}", fontsize=14)
        plt.legend()
        plt.tight_layout()
        plt.show()

        # Si les données sont en 3D ou plus, montrer aussi une projection PCA
        if dims > 2:
            print("\nRéduction de dimensionnalité avec PCA pour la visualisation...")
            X_pca = PCA(n_components=2, random_state=42).fit_transform(self.data)

            plt.figure(figsize=(10, 8))

            # Tracer les points de bruit en gris
            if np.any(noise_mask):
                plt.scatter(
                    X_pca[noise_mask, 0],
                    X_pca[noise_mask, 1],
                    c="lightgrey",
                    marker=".",
                    s=50,
                    alpha=0.5,
                    label="Bruit"
                )

            # Tracer chaque cluster avec une couleur différente
            color_idx = 0
            for label in sorted(unique_labels):
                if label == -1:
                    continue  # Déjà traité

                mask = self.labels == label
                plt.scatter(
                    X_pca[mask, 0],
                    X_pca[mask, 1],
                    c=[palette[color_idx]],
                    marker="o",
                    s=80,
                    alpha=0.8,
                    label=f"Cluster {label}"
                )
                color_idx += 1

            plt.title("Clustering HDBSCAN (projection PCA)", fontsize=14)
            plt.legend()
            plt.tight_layout()
            plt.show()

    def _visualize_cluster_probabilities(self):
        # Visualiser les probabilités d'appartenance aux clusters
        plt.figure(figsize=(10, 6))

        # Filtrer les points qui ne sont pas du bruit
        non_noise_mask = self.labels != -1
        if np.any(non_noise_mask):
            sns.histplot(self.model.probabilities_[non_noise_mask], bins=20, kde=True)
            plt.title("Distribution des probabilités d'appartenance aux clusters", fontsize=14)
            plt.xlabel("Probabilité")
            plt.ylabel("Fréquence")
            plt.tight_layout()
            plt.show()

    def run_hdbscan_clustering(self):
        self._optimize_parameters()
        self._fit_model()
        self._describe_clusters()
        self._visualize_clusters()
        self._visualize_cluster_probabilities()